package services

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"test-spark-backend/internal/database"
	"test-spark-backend/internal/models"
)

// TestService handles test-related business logic
type TestService struct {
	store      database.Store
	groqClient *GroqClient
}

// NewTestService creates a new test service
func NewTestService(store database.Store, groqClient *GroqClient) *TestService {
	return &TestService{
		store:      store,
		groqClient: groqClient,
	}
}

// TestWithFirstQuestion represents a test with its first question
type TestWithFirstQuestion struct {
	Test          *models.Test                     `json:"test"`
	FirstQuestion *models.TestQuestionWithDetails `json:"first_question"`
}

// CreateNewTest creates a new test with questions and returns the first question
func (s *TestService) CreateNewTest(ctx context.Context, userID uuid.UUID, req *models.CreateTestRequest) (*TestWithFirstQuestion, error) {
	// Create the test
	test := &models.Test{
		ID:                   uuid.New(),
		UserID:               userID,
		TestContextSubjectID: req.SubjectID,
		TestContextExamID:    req.ExamID,
		Status:               models.TestStatusPending,
		CreatedAt:            time.Now(),
	}

	if err := s.store.CreateTest(ctx, test); err != nil {
		return nil, fmt.Errorf("failed to create test: %w", err)
	}

	// Generate questions using Groq AI
	var topicIDs []int
	if len(req.TopicIDs) > 0 {
		topicIDs = req.TopicIDs
	} else {
		// If no specific topics, get all topics for the subject
		if req.SubjectID != nil {
			topics, err := s.store.GetTopicsBySubject(ctx, *req.SubjectID)
			if err != nil {
				return nil, fmt.Errorf("failed to get topics for subject: %w", err)
			}
			for _, topic := range topics {
				topicIDs = append(topicIDs, topic.ID)
			}
		} else {
			return nil, fmt.Errorf("either subject_id or topic_ids must be provided")
		}
	}

	// Generate questions using Groq
	questions, err := s.groqClient.GenerateMCQs(ctx, topicIDs, req.DifficultyLevels, req.NumQuestions)
	if err != nil {
		return nil, fmt.Errorf("failed to generate questions: %w", err)
	}

	// Store questions and create test_questions associations
	for i, question := range questions {
		// Create question
		if err := s.store.CreateQuestion(ctx, &question); err != nil {
			return nil, fmt.Errorf("failed to create question %d: %w", i, err)
		}

		// Create test_question association
		testQuestion := &models.TestQuestion{
			TestID:        test.ID,
			QuestionID:    question.ID,
			QuestionOrder: i + 1,
		}

		if err := s.store.CreateTestQuestion(ctx, testQuestion); err != nil {
			return nil, fmt.Errorf("failed to create test question %d: %w", i, err)
		}
	}

	// Update test status to in_progress
	if err := s.store.UpdateTestStatus(ctx, test.ID.String(), models.TestStatusInProgress); err != nil {
		return nil, fmt.Errorf("failed to update test status: %w", err)
	}

	// Get the first question with details
	firstQuestion, err := s.store.GetTestQuestionByOrder(ctx, test.ID.String(), 1)
	if err != nil {
		return nil, fmt.Errorf("failed to get first question: %w", err)
	}

	return &TestWithFirstQuestion{
		Test:          test,
		FirstQuestion: firstQuestion,
	}, nil
}

// RegenerateForWeakAreas creates a new test focusing on weak areas
func (s *TestService) RegenerateForWeakAreas(ctx context.Context, userID uuid.UUID, req *models.RegenerateTestRequest) (*TestWithFirstQuestion, error) {
	// Get weak topics for the user
	weakTopics, err := s.store.GetWeakestTopics(ctx, userID.String(), 10) // Get top 10 weakest topics
	if err != nil {
		return nil, fmt.Errorf("failed to get weak topics: %w", err)
	}

	if len(weakTopics) == 0 {
		return nil, fmt.Errorf("no performance data found to regenerate test")
	}

	// Extract topic IDs
	var topicIDs []int
	for _, topic := range weakTopics {
		topicIDs = append(topicIDs, topic.TopicID)
	}

	// Create test request focusing on weak areas
	createReq := &models.CreateTestRequest{
		NumQuestions:     req.NumQuestions,
		DifficultyLevels: []models.DifficultyLevel{models.DifficultyEasy, models.DifficultyMedium}, // Focus on easier questions for weak areas
		TopicIDs:         topicIDs,
	}

	return s.CreateNewTest(ctx, userID, createReq)
}

// SubmitAnswer processes a user's answer submission
func (s *TestService) SubmitAnswer(ctx context.Context, testID uuid.UUID, req *models.SubmitAnswerRequest) (*models.SubmitAnswerResponse, error) {
	// Get the question to check the correct answer
	question, err := s.store.GetQuestionByID(ctx, req.QuestionID.String())
	if err != nil {
		return nil, fmt.Errorf("failed to get question: %w", err)
	}

	// Parse question content to get correct answer
	content, err := question.GetContent()
	if err != nil {
		return nil, fmt.Errorf("failed to parse question content: %w", err)
	}

	// Check if answer is correct
	isCorrect := req.SelectedOptionIndex == content.CorrectOptionIndex

	// Get test_question ID
	testQuestionID, err := s.store.GetTestQuestionID(ctx, testID.String(), req.QuestionID.String())
	if err != nil {
		return nil, fmt.Errorf("failed to get test question ID: %w", err)
	}

	// Create user answer
	userAnswer := &models.UserAnswer{
		TestQuestionID:      testQuestionID,
		SelectedOptionIndex: req.SelectedOptionIndex,
		IsCorrect:           isCorrect,
		AnsweredAt:          time.Now(),
	}

	if err := s.store.CreateUserAnswer(ctx, userAnswer); err != nil {
		return nil, fmt.Errorf("failed to create user answer: %w", err)
	}

	return &models.SubmitAnswerResponse{
		IsCorrect:          isCorrect,
		CorrectOptionIndex: content.CorrectOptionIndex,
		Explanation:        content.Explanation,
	}, nil
}

// CompleteTest finalizes a test and creates the test result
func (s *TestService) CompleteTest(ctx context.Context, testID uuid.UUID, timeTakenSeconds int) (*models.CompleteTestResponse, error) {
	// Mark test as completed
	if err := s.store.CompleteTest(ctx, testID.String()); err != nil {
		return nil, fmt.Errorf("failed to complete test: %w", err)
	}

	// Get test details
	test, err := s.store.GetTestByID(ctx, testID.String())
	if err != nil {
		return nil, fmt.Errorf("failed to get test: %w", err)
	}

	// Get all user answers for this test
	answers, err := s.store.GetUserAnswersByTestID(ctx, testID.String())
	if err != nil {
		return nil, fmt.Errorf("failed to get user answers: %w", err)
	}

	// Calculate results
	totalQuestions := len(answers)
	correctAnswers := 0
	for _, answer := range answers {
		if answer.IsCorrect {
			correctAnswers++
		}
	}
	incorrectAnswers := totalQuestions - correctAnswers
	score := float64(correctAnswers) / float64(totalQuestions) * 100

	// Create test result
	testResult := &models.TestResult{
		ID:               uuid.New(),
		TestID:           testID,
		UserID:           test.UserID,
		Score:            score,
		TotalQuestions:   totalQuestions,
		CorrectAnswers:   correctAnswers,
		IncorrectAnswers: incorrectAnswers,
		TimeTakenSeconds: timeTakenSeconds,
	}

	if err := s.store.CreateTestResult(ctx, testResult); err != nil {
		return nil, fmt.Errorf("failed to create test result: %w", err)
	}

	return &models.CompleteTestResponse{
		TestID:      testID,
		Message:     "Test completed. Analysis is processing.",
		AnalysisURL: fmt.Sprintf("/api/v1/analysis/tests/%s", testID.String()),
	}, nil
}
