package services

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"test-spark-backend/internal/database"
	"test-spark-backend/internal/models"
)

// AnalyticsService handles analytics-related business logic
type AnalyticsService struct {
	store database.Store
}

// NewAnalyticsService creates a new analytics service
func NewAnalyticsService(store database.Store) *AnalyticsService {
	return &AnalyticsService{
		store: store,
	}
}

// GetTestAnalysis generates detailed analysis for a completed test
func (s *AnalyticsService) GetTestAnalysis(ctx context.Context, testID uuid.UUID) (*models.TestAnalysis, error) {
	// Get test result
	testResult, err := s.store.GetTestResult(ctx, testID.String())
	if err != nil {
		return nil, fmt.Errorf("failed to get test result: %w", err)
	}

	// Get test details
	test, err := s.store.GetTestByID(ctx, testID.String())
	if err != nil {
		return nil, fmt.Errorf("failed to get test: %w", err)
	}

	// Get all test questions with details
	testQuestions, err := s.store.GetTestQuestions(ctx, testID.String())
	if err != nil {
		return nil, fmt.Errorf("failed to get test questions: %w", err)
	}

	// Get user answers
	userAnswers, err := s.store.GetUserAnswersByTestID(ctx, testID.String())
	if err != nil {
		return nil, fmt.Errorf("failed to get user answers: %w", err)
	}

	// Create answer map for quick lookup
	answerMap := make(map[int]*models.UserAnswer)
	for _, answer := range userAnswers {
		answerMap[answer.TestQuestionID] = &answer
	}

	// Build question breakdown
	var questionBreakdown []models.QuestionAnalysisDetail
	topicPerformanceMap := make(map[int]*models.TopicPerformanceDetail)

	for _, tq := range testQuestions {
		// Get question details
		questionWithDetails, err := s.store.GetTestQuestionByOrder(ctx, testID.String(), tq.QuestionOrder)
		if err != nil {
			continue // Skip if we can't get details
		}

		// Get user answer for this question
		userAnswer, hasAnswer := answerMap[tq.ID]
		if !hasAnswer {
			continue // Skip if no answer found
		}

		// Add to question breakdown
		questionDetail := models.QuestionAnalysisDetail{
			QuestionID:    tq.QuestionID,
			QuestionOrder: tq.QuestionOrder,
			TopicName:     questionWithDetails.TopicName,
			Difficulty:    questionWithDetails.Difficulty,
			UserAnswer:    userAnswer.SelectedOptionIndex,
			CorrectAnswer: questionWithDetails.Question.CorrectOptionIndex,
			IsCorrect:     userAnswer.IsCorrect,
			Question:      questionWithDetails.Question.Question,
			Options:       questionWithDetails.Question.Options,
			Explanation:   questionWithDetails.Question.Explanation,
		}
		questionBreakdown = append(questionBreakdown, questionDetail)

		// Update topic performance tracking
		topicID := questionWithDetails.TopicID
		if _, exists := topicPerformanceMap[topicID]; !exists {
			topicPerformanceMap[topicID] = &models.TopicPerformanceDetail{
				TopicID:        topicID,
				TopicName:      questionWithDetails.TopicName,
				TotalQuestions: 0,
				CorrectAnswers: 0,
			}
		}

		topicPerformanceMap[topicID].TotalQuestions++
		if userAnswer.IsCorrect {
			topicPerformanceMap[topicID].CorrectAnswers++
		}
	}

	// Calculate accuracy rates and categorize topics
	var strongTopics, weakTopics []models.TopicPerformanceDetail
	for _, topicPerf := range topicPerformanceMap {
		topicPerf.AccuracyRate = float64(topicPerf.CorrectAnswers) / float64(topicPerf.TotalQuestions) * 100

		// Get overall proficiency score for this topic
		if topicProficiency, err := s.store.GetTopicPerformance(ctx, test.UserID.String(), topicPerf.TopicID); err == nil {
			topicPerf.ProficiencyScore = topicProficiency.ProficiencyScore
		}

		// Categorize as strong (>70%) or weak (<=70%)
		if topicPerf.AccuracyRate > 70 {
			strongTopics = append(strongTopics, *topicPerf)
		} else {
			weakTopics = append(weakTopics, *topicPerf)
		}
	}

	return &models.TestAnalysis{
		TestID:            testID,
		Score:             testResult.Score,
		TotalQuestions:    testResult.TotalQuestions,
		CorrectAnswers:    testResult.CorrectAnswers,
		IncorrectAnswers:  testResult.IncorrectAnswers,
		TimeTakenSeconds:  testResult.TimeTakenSeconds,
		StrongTopics:      strongTopics,
		WeakTopics:        weakTopics,
		QuestionBreakdown: questionBreakdown,
	}, nil
}
