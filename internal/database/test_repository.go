package database

import (
	"context"
	"fmt"

	"test-spark-backend/internal/models"
)

// CreateTest creates a new test in the database
func (s *SQLStore) CreateTest(ctx context.Context, test *models.Test) error {
	query := `
		INSERT INTO tests (id, user_id, test_context_subject_id, test_context_exam_id, status, created_at, completed_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7)`

	_, err := s.db.Exec(ctx, query,
		test.ID,
		test.UserID,
		test.TestContextSubjectID,
		test.TestContextExamID,
		test.Status,
		test.CreatedAt,
		test.CompletedAt,
	)
	if err != nil {
		return fmt.Errorf("failed to create test: %w", err)
	}

	return nil
}

// GetTestByID retrieves a test by its ID
func (s *SQLStore) GetTestByID(ctx context.Context, id string) (*models.Test, error) {
	query := `
		SELECT id, user_id, test_context_subject_id, test_context_exam_id, status, created_at, completed_at
		FROM tests
		WHERE id = $1`

	var test models.Test
	err := s.db.QueryRow(ctx, query, id).Scan(
		&test.ID,
		&test.UserID,
		&test.TestContextSubjectID,
		&test.TestContextExamID,
		&test.Status,
		&test.CreatedAt,
		&test.CompletedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get test by ID: %w", err)
	}

	return &test, nil
}

// GetTestsByUserID retrieves all tests for a specific user
func (s *SQLStore) GetTestsByUserID(ctx context.Context, userID string) ([]models.Test, error) {
	query := `
		SELECT id, user_id, test_context_subject_id, test_context_exam_id, status, created_at, completed_at
		FROM tests
		WHERE user_id = $1
		ORDER BY created_at DESC`

	rows, err := s.db.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get tests by user ID: %w", err)
	}
	defer rows.Close()

	var tests []models.Test
	for rows.Next() {
		var test models.Test
		err := rows.Scan(
			&test.ID,
			&test.UserID,
			&test.TestContextSubjectID,
			&test.TestContextExamID,
			&test.Status,
			&test.CreatedAt,
			&test.CompletedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan test: %w", err)
		}
		tests = append(tests, test)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating tests: %w", err)
	}

	return tests, nil
}

// UpdateTestStatus updates the status of a test
func (s *SQLStore) UpdateTestStatus(ctx context.Context, testID string, status models.TestStatus) error {
	query := `
		UPDATE tests
		SET status = $2
		WHERE id = $1`

	result, err := s.db.Exec(ctx, query, testID, status)
	if err != nil {
		return fmt.Errorf("failed to update test status: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return fmt.Errorf("test not found")
	}

	return nil
}

// CompleteTest marks a test as completed and sets the completion time
func (s *SQLStore) CompleteTest(ctx context.Context, testID string) error {
	query := `
		UPDATE tests
		SET status = $2, completed_at = NOW()
		WHERE id = $1`

	result, err := s.db.Exec(ctx, query, testID, models.TestStatusCompleted)
	if err != nil {
		return fmt.Errorf("failed to complete test: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return fmt.Errorf("test not found")
	}

	return nil
}

// CreateTestQuestion creates a new test question association
func (s *SQLStore) CreateTestQuestion(ctx context.Context, testQuestion *models.TestQuestion) error {
	query := `
		INSERT INTO test_questions (test_id, question_id, question_order)
		VALUES ($1, $2, $3)
		RETURNING id`

	err := s.db.QueryRow(ctx, query,
		testQuestion.TestID,
		testQuestion.QuestionID,
		testQuestion.QuestionOrder,
	).Scan(&testQuestion.ID)
	if err != nil {
		return fmt.Errorf("failed to create test question: %w", err)
	}

	return nil
}

// GetTestQuestions retrieves all questions for a specific test
func (s *SQLStore) GetTestQuestions(ctx context.Context, testID string) ([]models.TestQuestion, error) {
	query := `
		SELECT id, test_id, question_id, question_order
		FROM test_questions
		WHERE test_id = $1
		ORDER BY question_order`

	rows, err := s.db.Query(ctx, query, testID)
	if err != nil {
		return nil, fmt.Errorf("failed to get test questions: %w", err)
	}
	defer rows.Close()

	var testQuestions []models.TestQuestion
	for rows.Next() {
		var testQuestion models.TestQuestion
		err := rows.Scan(
			&testQuestion.ID,
			&testQuestion.TestID,
			&testQuestion.QuestionID,
			&testQuestion.QuestionOrder,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan test question: %w", err)
		}
		testQuestions = append(testQuestions, testQuestion)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating test questions: %w", err)
	}

	return testQuestions, nil
}

// GetTestQuestionByOrder retrieves a specific question by its order in a test with full details
func (s *SQLStore) GetTestQuestionByOrder(ctx context.Context, testID string, questionOrder int) (*models.TestQuestionWithDetails, error) {
	query := `
		SELECT
			tq.id, tq.test_id, tq.question_id, tq.question_order,
			q.content, q.difficulty,
			t.name as topic_name
		FROM test_questions tq
		JOIN questions q ON tq.question_id = q.id
		JOIN topics t ON q.topic_id = t.id
		WHERE tq.test_id = $1 AND tq.question_order = $2`

	var testQuestion models.TestQuestionWithDetails
	var content []byte

	err := s.db.QueryRow(ctx, query, testID, questionOrder).Scan(
		&testQuestion.ID,
		&testQuestion.TestID,
		&testQuestion.QuestionID,
		&testQuestion.QuestionOrder,
		&content,
		&testQuestion.Difficulty,
		&testQuestion.TopicName,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get test question by order: %w", err)
	}

	// Parse the JSONB content
	var questionContent models.QuestionContent
	if err := json.Unmarshal(content, &questionContent); err != nil {
		return nil, fmt.Errorf("failed to parse question content: %w", err)
	}
	testQuestion.Question = questionContent

	return &testQuestion, nil
}
