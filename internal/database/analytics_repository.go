package database

import (
	"context"
	"fmt"

	"test-spark-backend/internal/models"
)

// CreateTestResult creates a new test result in the database
func (s *SQLStore) CreateTestResult(ctx context.Context, result *models.TestResult) error {
	query := `
		INSERT INTO test_results (id, test_id, user_id, score, total_questions, correct_answers, incorrect_answers, time_taken_seconds)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`

	_, err := s.db.Exec(ctx, query,
		result.ID,
		result.TestID,
		result.UserID,
		result.Score,
		result.TotalQuestions,
		result.CorrectAnswers,
		result.IncorrectAnswers,
		result.TimeTakenSeconds,
	)
	if err != nil {
		return fmt.Errorf("failed to create test result: %w", err)
	}

	return nil
}

// GetTestResult retrieves a test result by test ID
func (s *SQLStore) GetTestResult(ctx context.Context, testID string) (*models.TestResult, error) {
	query := `
		SELECT id, test_id, user_id, score, total_questions, correct_answers, incorrect_answers, time_taken_seconds
		FROM test_results
		WHERE test_id = $1`

	var result models.TestResult
	err := s.db.QueryRow(ctx, query, testID).Scan(
		&result.ID,
		&result.TestID,
		&result.UserID,
		&result.Score,
		&result.TotalQuestions,
		&result.CorrectAnswers,
		&result.IncorrectAnswers,
		&result.TimeTakenSeconds,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get test result: %w", err)
	}

	return &result, nil
}

// GetUserTestHistory retrieves test history for a user using the view
func (s *SQLStore) GetUserTestHistory(ctx context.Context, userID string, limit int) ([]models.UserTestHistory, error) {
	query := `
		SELECT test_id, user_id, status, created_at, completed_at, subject_name, exam_name, 
		       score, total_questions, correct_answers, incorrect_answers, time_taken_seconds
		FROM v_user_test_history
		WHERE user_id = $1
		ORDER BY created_at DESC
		LIMIT $2`

	rows, err := s.db.Query(ctx, query, userID, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get user test history: %w", err)
	}
	defer rows.Close()

	var history []models.UserTestHistory
	for rows.Next() {
		var test models.UserTestHistory
		err := rows.Scan(
			&test.TestID,
			&test.UserID,
			&test.Status,
			&test.CreatedAt,
			&test.CompletedAt,
			&test.SubjectName,
			&test.ExamName,
			&test.Score,
			&test.TotalQuestions,
			&test.CorrectAnswers,
			&test.IncorrectAnswers,
			&test.TimeTakenSeconds,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan user test history: %w", err)
		}
		history = append(history, test)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating user test history: %w", err)
	}

	return history, nil
}

// GetTopicPerformance retrieves topic performance summary for a user and topic
func (s *SQLStore) GetTopicPerformance(ctx context.Context, userID string, topicID int) (*models.TopicPerformanceSummary, error) {
	query := `
		SELECT id, user_id, topic_id, total_attempted, total_correct, proficiency_score, last_tested_at
		FROM topic_performance_summary
		WHERE user_id = $1 AND topic_id = $2`

	var performance models.TopicPerformanceSummary
	err := s.db.QueryRow(ctx, query, userID, topicID).Scan(
		&performance.ID,
		&performance.UserID,
		&performance.TopicID,
		&performance.TotalAttempted,
		&performance.TotalCorrect,
		&performance.ProficiencyScore,
		&performance.LastTestedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get topic performance: %w", err)
	}

	return &performance, nil
}

// GetUserTopicPerformances retrieves all topic performances for a user with topic names
func (s *SQLStore) GetUserTopicPerformances(ctx context.Context, userID string) ([]models.TopicPerformanceWithName, error) {
	query := `
		SELECT 
			tps.topic_id, t.name as topic_name, s.name as subject_name,
			tps.total_attempted, tps.total_correct, tps.proficiency_score, tps.last_tested_at
		FROM topic_performance_summary tps
		JOIN topics t ON tps.topic_id = t.id
		JOIN subjects s ON t.subject_id = s.id
		WHERE tps.user_id = $1
		ORDER BY tps.proficiency_score DESC`

	rows, err := s.db.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user topic performances: %w", err)
	}
	defer rows.Close()

	var performances []models.TopicPerformanceWithName
	for rows.Next() {
		var performance models.TopicPerformanceWithName
		err := rows.Scan(
			&performance.TopicID,
			&performance.TopicName,
			&performance.SubjectName,
			&performance.TotalAttempted,
			&performance.TotalCorrect,
			&performance.ProficiencyScore,
			&performance.LastTestedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan topic performance: %w", err)
		}
		performances = append(performances, performance)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating topic performances: %w", err)
	}

	return performances, nil
}

// GetWeakestTopics retrieves the weakest performing topics for a user
func (s *SQLStore) GetWeakestTopics(ctx context.Context, userID string, limit int) ([]models.TopicPerformanceWithName, error) {
	query := `
		SELECT 
			tps.topic_id, t.name as topic_name, s.name as subject_name,
			tps.total_attempted, tps.total_correct, tps.proficiency_score, tps.last_tested_at
		FROM topic_performance_summary tps
		JOIN topics t ON tps.topic_id = t.id
		JOIN subjects s ON t.subject_id = s.id
		WHERE tps.user_id = $1 AND tps.total_attempted > 0
		ORDER BY tps.proficiency_score ASC
		LIMIT $2`

	rows, err := s.db.Query(ctx, query, userID, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get weakest topics: %w", err)
	}
	defer rows.Close()

	var topics []models.TopicPerformanceWithName
	for rows.Next() {
		var topic models.TopicPerformanceWithName
		err := rows.Scan(
			&topic.TopicID,
			&topic.TopicName,
			&topic.SubjectName,
			&topic.TotalAttempted,
			&topic.TotalCorrect,
			&topic.ProficiencyScore,
			&topic.LastTestedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan weakest topic: %w", err)
		}
		topics = append(topics, topic)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating weakest topics: %w", err)
	}

	return topics, nil
}

// GetDashboardSummary retrieves dashboard summary data for a user
func (s *SQLStore) GetDashboardSummary(ctx context.Context, userID string) (*models.DashboardSummary, error) {
	// Get overall statistics
	var summary models.DashboardSummary
	
	// Get total tests and average score
	statsQuery := `
		SELECT 
			COUNT(*) as total_tests,
			COALESCE(AVG(score), 0) as average_score
		FROM test_results
		WHERE user_id = $1`
	
	err := s.db.QueryRow(ctx, statsQuery, userID).Scan(
		&summary.TotalTestsTaken,
		&summary.AverageScore,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get dashboard stats: %w", err)
	}

	// Get overall proficiency (average of all topic proficiency scores)
	proficiencyQuery := `
		SELECT COALESCE(AVG(proficiency_score), 0)
		FROM topic_performance_summary
		WHERE user_id = $1 AND total_attempted > 0`
	
	err = s.db.QueryRow(ctx, proficiencyQuery, userID).Scan(&summary.OverallProficiency)
	if err != nil {
		return nil, fmt.Errorf("failed to get overall proficiency: %w", err)
	}

	// Get strongest and weakest subjects
	subjectQuery := `
		SELECT 
			s.name,
			AVG(tps.proficiency_score) as avg_proficiency
		FROM topic_performance_summary tps
		JOIN topics t ON tps.topic_id = t.id
		JOIN subjects s ON t.subject_id = s.id
		WHERE tps.user_id = $1 AND tps.total_attempted > 0
		GROUP BY s.id, s.name
		ORDER BY avg_proficiency DESC`
	
	rows, err := s.db.Query(ctx, subjectQuery, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get subject performance: %w", err)
	}
	defer rows.Close()

	var subjects []struct {
		Name        string
		Proficiency float64
	}
	
	for rows.Next() {
		var subject struct {
			Name        string
			Proficiency float64
		}
		err := rows.Scan(&subject.Name, &subject.Proficiency)
		if err != nil {
			return nil, fmt.Errorf("failed to scan subject performance: %w", err)
		}
		subjects = append(subjects, subject)
	}

	if len(subjects) > 0 {
		summary.StrongestSubject = &subjects[0].Name
		summary.WeakestSubject = &subjects[len(subjects)-1].Name
	}

	// Get recent tests (limit 5)
	recentTests, err := s.GetUserTestHistory(ctx, userID, 5)
	if err != nil {
		return nil, fmt.Errorf("failed to get recent tests: %w", err)
	}
	summary.RecentTests = recentTests

	// Get topic performance
	topicPerformance, err := s.GetUserTopicPerformances(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get topic performance: %w", err)
	}
	summary.TopicPerformance = topicPerformance

	return &summary, nil
}
