package database

import (
	"context"
	"fmt"

	"test-spark-backend/internal/models"
)

// CreateUserAnswer creates a new user answer in the database
func (s *SQLStore) CreateUserAnswer(ctx context.Context, answer *models.UserAnswer) error {
	query := `
		INSERT INTO user_answers (test_question_id, selected_option_index, is_correct, answered_at)
		VALUES ($1, $2, $3, $4)
		RETURNING id`

	err := s.db.QueryRow(ctx, query,
		answer.TestQuestionID,
		answer.SelectedOptionIndex,
		answer.IsCorrect,
		answer.AnsweredAt,
	).Scan(&answer.ID)
	if err != nil {
		return fmt.Errorf("failed to create user answer: %w", err)
	}

	return nil
}

// GetUserAnswer retrieves a user answer by test question ID
func (s *SQLStore) GetUserAnswer(ctx context.Context, testQuestionID int) (*models.UserAnswer, error) {
	query := `
		SELECT id, test_question_id, selected_option_index, is_correct, answered_at
		FROM user_answers
		WHERE test_question_id = $1`

	var answer models.UserAnswer
	err := s.db.QueryRow(ctx, query, testQuestionID).Scan(
		&answer.ID,
		&answer.TestQuestionID,
		&answer.SelectedOptionIndex,
		&answer.IsCorrect,
		&answer.AnsweredAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get user answer: %w", err)
	}

	return &answer, nil
}

// GetUserAnswersByTestID retrieves all user answers for a specific test
func (s *SQLStore) GetUserAnswersByTestID(ctx context.Context, testID string) ([]models.UserAnswer, error) {
	query := `
		SELECT ua.id, ua.test_question_id, ua.selected_option_index, ua.is_correct, ua.answered_at
		FROM user_answers ua
		JOIN test_questions tq ON ua.test_question_id = tq.id
		WHERE tq.test_id = $1
		ORDER BY tq.question_order`

	rows, err := s.db.Query(ctx, query, testID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user answers by test ID: %w", err)
	}
	defer rows.Close()

	var answers []models.UserAnswer
	for rows.Next() {
		var answer models.UserAnswer
		err := rows.Scan(
			&answer.ID,
			&answer.TestQuestionID,
			&answer.SelectedOptionIndex,
			&answer.IsCorrect,
			&answer.AnsweredAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan user answer: %w", err)
		}
		answers = append(answers, answer)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating user answers: %w", err)
	}

	return answers, nil
}

// GetTestQuestionID retrieves the test_question ID for a given test and question
func (s *SQLStore) GetTestQuestionID(ctx context.Context, testID string, questionID string) (int, error) {
	query := `
		SELECT id
		FROM test_questions
		WHERE test_id = $1 AND question_id = $2`

	var testQuestionID int
	err := s.db.QueryRow(ctx, query, testID, questionID).Scan(&testQuestionID)
	if err != nil {
		return 0, fmt.Errorf("failed to get test question ID: %w", err)
	}

	return testQuestionID, nil
}
