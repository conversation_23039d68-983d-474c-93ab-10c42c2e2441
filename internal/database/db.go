package database

import (
	"context"

	"github.com/jackc/pgx/v5/pgxpool"
	"test-spark-backend/internal/models"
)

// Store interface embeds all repository interfaces
type Store interface {
	UserRepository
	AuthRepository
	ContentRepository
	TestRepository
	QuestionRepository
	AnswerRepository
	AnalyticsRepository
}

// UserRepository defines methods for user-related database operations
type UserRepository interface {
	CreateUser(ctx context.Context, user *models.User) error
	GetUserByID(ctx context.Context, id string) (*models.User, error)
	GetUserByEmail(ctx context.Context, email string) (*models.User, error)
	UpdateUser(ctx context.Context, user *models.User) error
	CreateUserProfile(ctx context.Context, profile *models.UserProfile) error
	GetUserProfile(ctx context.Context, userID string) (*models.UserProfile, error)
	UpdateUserProfile(ctx context.Context, profile *models.UserProfile) error
	GetUserWithProfile(ctx context.Context, userID string) (*models.UserWithProfile, error)
}

// AuthRepository defines methods for authentication-related database operations
type AuthRepository interface {
	CreateRefreshToken(ctx context.Context, token *models.RefreshToken) error
	GetRefreshToken(ctx context.Context, tokenHash string) (*models.RefreshToken, error)
	RevokeRefreshToken(ctx context.Context, tokenHash string) error
	RevokeAllUserRefreshTokens(ctx context.Context, userID string) error
}

// ContentRepository defines methods for content-related database operations
type ContentRepository interface {
	GetSubjects(ctx context.Context) ([]models.Subject, error)
	GetSubjectByID(ctx context.Context, id int) (*models.Subject, error)
	GetExams(ctx context.Context) ([]models.Exam, error)
	GetExamByID(ctx context.Context, id int) (*models.Exam, error)
	GetTopicsBySubject(ctx context.Context, subjectID int) ([]models.Topic, error)
	GetTopicByID(ctx context.Context, id int) (*models.Topic, error)
	GetUserPreferences(ctx context.Context, userID string) ([]models.UserPreference, error)
	CreateUserPreference(ctx context.Context, preference *models.UserPreference) error
}

// TestRepository defines methods for test-related database operations
type TestRepository interface {
	CreateTest(ctx context.Context, test *models.Test) error
	GetTestByID(ctx context.Context, id string) (*models.Test, error)
	GetTestsByUserID(ctx context.Context, userID string) ([]models.Test, error)
	UpdateTestStatus(ctx context.Context, testID string, status models.TestStatus) error
	CompleteTest(ctx context.Context, testID string) error
	CreateTestQuestion(ctx context.Context, testQuestion *models.TestQuestion) error
	GetTestQuestions(ctx context.Context, testID string) ([]models.TestQuestion, error)
	GetTestQuestionByOrder(ctx context.Context, testID string, questionOrder int) (*models.TestQuestionWithDetails, error)
}

// QuestionRepository defines methods for question-related database operations
type QuestionRepository interface {
	CreateQuestion(ctx context.Context, question *models.Question) error
	GetQuestionByID(ctx context.Context, id string) (*models.Question, error)
	GetQuestionsByTopicID(ctx context.Context, topicID int) ([]models.Question, error)
	GetQuestionsByDifficulty(ctx context.Context, difficulty models.DifficultyLevel) ([]models.Question, error)
	GetRandomQuestions(ctx context.Context, topicIDs []int, difficulties []models.DifficultyLevel, limit int) ([]models.Question, error)
}

// AnswerRepository defines methods for answer-related database operations
type AnswerRepository interface {
	CreateUserAnswer(ctx context.Context, answer *models.UserAnswer) error
	GetUserAnswer(ctx context.Context, testQuestionID int) (*models.UserAnswer, error)
	GetUserAnswersByTestID(ctx context.Context, testID string) ([]models.UserAnswer, error)
	GetTestQuestionID(ctx context.Context, testID string, questionID string) (int, error)
}

// AnalyticsRepository defines methods for analytics-related database operations
type AnalyticsRepository interface {
	CreateTestResult(ctx context.Context, result *models.TestResult) error
	GetTestResult(ctx context.Context, testID string) (*models.TestResult, error)
	GetUserTestHistory(ctx context.Context, userID string, limit int) ([]models.UserTestHistory, error)
	GetTopicPerformance(ctx context.Context, userID string, topicID int) (*models.TopicPerformanceSummary, error)
	GetUserTopicPerformances(ctx context.Context, userID string) ([]models.TopicPerformanceWithName, error)
	GetWeakestTopics(ctx context.Context, userID string, limit int) ([]models.TopicPerformanceWithName, error)
	GetDashboardSummary(ctx context.Context, userID string) (*models.DashboardSummary, error)
}

// SQLStore implements the Store interface using PostgreSQL
type SQLStore struct {
	db *pgxpool.Pool
}

// NewSQLStore creates a new SQLStore instance
func NewSQLStore(db *pgxpool.Pool) Store {
	return &SQLStore{
		db: db,
	}
}

// GetDB returns the underlying database connection pool
func (s *SQLStore) GetDB() *pgxpool.Pool {
	return s.db
}
