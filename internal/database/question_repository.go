package database

import (
	"context"
	"fmt"
	"strings"

	"test-spark-backend/internal/models"
)

// CreateQuestion creates a new question in the database
func (s *SQLStore) CreateQuestion(ctx context.Context, question *models.Question) error {
	query := `
		INSERT INTO questions (id, topic_id, content, difficulty, author_ai_model, created_at)
		VALUES ($1, $2, $3, $4, $5, $6)`

	_, err := s.db.Exec(ctx, query,
		question.ID,
		question.TopicID,
		question.Content,
		question.Difficulty,
		question.AuthorAIModel,
		question.CreatedAt,
	)
	if err != nil {
		return fmt.Errorf("failed to create question: %w", err)
	}

	return nil
}

// GetQuestionByID retrieves a question by its ID
func (s *SQLStore) GetQuestionByID(ctx context.Context, id string) (*models.Question, error) {
	query := `
		SELECT id, topic_id, content, difficulty, author_ai_model, created_at
		FROM questions
		WHERE id = $1`

	var question models.Question
	err := s.db.QueryRow(ctx, query, id).Scan(
		&question.ID,
		&question.TopicID,
		&question.Content,
		&question.Difficulty,
		&question.AuthorAIModel,
		&question.CreatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get question by ID: %w", err)
	}

	return &question, nil
}

// GetQuestionsByTopicID retrieves all questions for a specific topic
func (s *SQLStore) GetQuestionsByTopicID(ctx context.Context, topicID int) ([]models.Question, error) {
	query := `
		SELECT id, topic_id, content, difficulty, author_ai_model, created_at
		FROM questions
		WHERE topic_id = $1
		ORDER BY created_at DESC`

	rows, err := s.db.Query(ctx, query, topicID)
	if err != nil {
		return nil, fmt.Errorf("failed to get questions by topic ID: %w", err)
	}
	defer rows.Close()

	var questions []models.Question
	for rows.Next() {
		var question models.Question
		err := rows.Scan(
			&question.ID,
			&question.TopicID,
			&question.Content,
			&question.Difficulty,
			&question.AuthorAIModel,
			&question.CreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan question: %w", err)
		}
		questions = append(questions, question)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating questions: %w", err)
	}

	return questions, nil
}

// GetQuestionsByDifficulty retrieves all questions with a specific difficulty level
func (s *SQLStore) GetQuestionsByDifficulty(ctx context.Context, difficulty models.DifficultyLevel) ([]models.Question, error) {
	query := `
		SELECT id, topic_id, content, difficulty, author_ai_model, created_at
		FROM questions
		WHERE difficulty = $1
		ORDER BY created_at DESC`

	rows, err := s.db.Query(ctx, query, difficulty)
	if err != nil {
		return nil, fmt.Errorf("failed to get questions by difficulty: %w", err)
	}
	defer rows.Close()

	var questions []models.Question
	for rows.Next() {
		var question models.Question
		err := rows.Scan(
			&question.ID,
			&question.TopicID,
			&question.Content,
			&question.Difficulty,
			&question.AuthorAIModel,
			&question.CreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan question: %w", err)
		}
		questions = append(questions, question)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating questions: %w", err)
	}

	return questions, nil
}

// GetRandomQuestions retrieves random questions based on topic IDs and difficulty levels
func (s *SQLStore) GetRandomQuestions(ctx context.Context, topicIDs []int, difficulties []models.DifficultyLevel, limit int) ([]models.Question, error) {
	if len(topicIDs) == 0 || len(difficulties) == 0 {
		return nil, fmt.Errorf("topicIDs and difficulties cannot be empty")
	}

	// Build the query with placeholders for topic IDs and difficulties
	topicPlaceholders := make([]string, len(topicIDs))
	difficultyPlaceholders := make([]string, len(difficulties))
	
	args := make([]interface{}, 0, len(topicIDs)+len(difficulties)+1)
	argIndex := 1

	// Add topic IDs to args and create placeholders
	for i, topicID := range topicIDs {
		topicPlaceholders[i] = fmt.Sprintf("$%d", argIndex)
		args = append(args, topicID)
		argIndex++
	}

	// Add difficulties to args and create placeholders
	for i, difficulty := range difficulties {
		difficultyPlaceholders[i] = fmt.Sprintf("$%d", argIndex)
		args = append(args, difficulty)
		argIndex++
	}

	// Add limit to args
	args = append(args, limit)

	query := fmt.Sprintf(`
		SELECT id, topic_id, content, difficulty, author_ai_model, created_at
		FROM questions
		WHERE topic_id IN (%s) AND difficulty IN (%s)
		ORDER BY RANDOM()
		LIMIT $%d`,
		strings.Join(topicPlaceholders, ","),
		strings.Join(difficultyPlaceholders, ","),
		argIndex,
	)

	rows, err := s.db.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to get random questions: %w", err)
	}
	defer rows.Close()

	var questions []models.Question
	for rows.Next() {
		var question models.Question
		err := rows.Scan(
			&question.ID,
			&question.TopicID,
			&question.Content,
			&question.Difficulty,
			&question.AuthorAIModel,
			&question.CreatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan question: %w", err)
		}
		questions = append(questions, question)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating questions: %w", err)
	}

	return questions, nil
}
