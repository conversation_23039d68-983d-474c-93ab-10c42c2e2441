package database

import (
	"context"
	"fmt"

	"test-spark-backend/internal/models"
)

// GetSubjects retrieves all subjects from the database
func (s *SQLStore) GetSubjects(ctx context.Context) ([]models.Subject, error) {
	query := `
		SELECT id, name, description
		FROM subjects
		ORDER BY name`

	rows, err := s.db.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to get subjects: %w", err)
	}
	defer rows.Close()

	var subjects []models.Subject
	for rows.Next() {
		var subject models.Subject
		err := rows.Scan(&subject.ID, &subject.Name, &subject.Description)
		if err != nil {
			return nil, fmt.Errorf("failed to scan subject: %w", err)
		}
		subjects = append(subjects, subject)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating subjects: %w", err)
	}

	return subjects, nil
}

// GetSubjectByID retrieves a subject by its ID
func (s *SQLStore) GetSubjectByID(ctx context.Context, id int) (*models.Subject, error) {
	query := `
		SELECT id, name, description
		FROM subjects
		WHERE id = $1`

	var subject models.Subject
	err := s.db.QueryRow(ctx, query, id).Scan(
		&subject.ID,
		&subject.Name,
		&subject.Description,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get subject by ID: %w", err)
	}

	return &subject, nil
}

// GetExams retrieves all exams from the database
func (s *SQLStore) GetExams(ctx context.Context) ([]models.Exam, error) {
	query := `
		SELECT id, name, description
		FROM exams
		ORDER BY name`

	rows, err := s.db.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to get exams: %w", err)
	}
	defer rows.Close()

	var exams []models.Exam
	for rows.Next() {
		var exam models.Exam
		err := rows.Scan(&exam.ID, &exam.Name, &exam.Description)
		if err != nil {
			return nil, fmt.Errorf("failed to scan exam: %w", err)
		}
		exams = append(exams, exam)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating exams: %w", err)
	}

	return exams, nil
}

// GetExamByID retrieves an exam by its ID
func (s *SQLStore) GetExamByID(ctx context.Context, id int) (*models.Exam, error) {
	query := `
		SELECT id, name, description
		FROM exams
		WHERE id = $1`

	var exam models.Exam
	err := s.db.QueryRow(ctx, query, id).Scan(
		&exam.ID,
		&exam.Name,
		&exam.Description,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get exam by ID: %w", err)
	}

	return &exam, nil
}

// GetTopicsBySubject retrieves all topics for a specific subject
func (s *SQLStore) GetTopicsBySubject(ctx context.Context, subjectID int) ([]models.Topic, error) {
	query := `
		SELECT id, subject_id, name, description
		FROM topics
		WHERE subject_id = $1
		ORDER BY name`

	rows, err := s.db.Query(ctx, query, subjectID)
	if err != nil {
		return nil, fmt.Errorf("failed to get topics by subject: %w", err)
	}
	defer rows.Close()

	var topics []models.Topic
	for rows.Next() {
		var topic models.Topic
		err := rows.Scan(&topic.ID, &topic.SubjectID, &topic.Name, &topic.Description)
		if err != nil {
			return nil, fmt.Errorf("failed to scan topic: %w", err)
		}
		topics = append(topics, topic)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating topics: %w", err)
	}

	return topics, nil
}

// GetTopicByID retrieves a topic by its ID
func (s *SQLStore) GetTopicByID(ctx context.Context, id int) (*models.Topic, error) {
	query := `
		SELECT id, subject_id, name, description
		FROM topics
		WHERE id = $1`

	var topic models.Topic
	err := s.db.QueryRow(ctx, query, id).Scan(
		&topic.ID,
		&topic.SubjectID,
		&topic.Name,
		&topic.Description,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get topic by ID: %w", err)
	}

	return &topic, nil
}

// GetUserPreferences retrieves all preferences for a specific user
func (s *SQLStore) GetUserPreferences(ctx context.Context, userID string) ([]models.UserPreference, error) {
	query := `
		SELECT user_id, subject_id, exam_id
		FROM user_preferences
		WHERE user_id = $1`

	rows, err := s.db.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user preferences: %w", err)
	}
	defer rows.Close()

	var preferences []models.UserPreference
	for rows.Next() {
		var preference models.UserPreference
		err := rows.Scan(&preference.UserID, &preference.SubjectID, &preference.ExamID)
		if err != nil {
			return nil, fmt.Errorf("failed to scan user preference: %w", err)
		}
		preferences = append(preferences, preference)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating user preferences: %w", err)
	}

	return preferences, nil
}

// CreateUserPreference creates a new user preference
func (s *SQLStore) CreateUserPreference(ctx context.Context, preference *models.UserPreference) error {
	query := `
		INSERT INTO user_preferences (user_id, subject_id, exam_id)
		VALUES ($1, $2, $3)
		ON CONFLICT (user_id, subject_id, exam_id) DO NOTHING`

	_, err := s.db.Exec(ctx, query, preference.UserID, preference.SubjectID, preference.ExamID)
	if err != nil {
		return fmt.Errorf("failed to create user preference: %w", err)
	}

	return nil
}
