package database

import (
	"context"
	"fmt"

	"test-spark-backend/internal/models"
)

// <PERSON><PERSON><PERSON><PERSON> creates a new user in the database
func (s *SQLStore) CreateUser(ctx context.Context, user *models.User) error {
	query := `
		INSERT INTO users (id, email, password_hash, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5)`

	_, err := s.db.Exec(ctx, query, user.ID, user.Email, user.PasswordHash, user.CreatedAt, user.UpdatedAt)
	if err != nil {
		return fmt.Errorf("failed to create user: %w", err)
	}

	return nil
}

// GetUserByID retrieves a user by their ID
func (s *SQLStore) GetUserByID(ctx context.Context, id string) (*models.User, error) {
	query := `
		SELECT id, email, password_hash, created_at, updated_at
		FROM users
		WHERE id = $1`

	var user models.User
	err := s.db.QueryRow(ctx, query, id).Scan(
		&user.ID,
		&user.Email,
		&user.PasswordHash,
		&user.CreatedAt,
		&user.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get user by ID: %w", err)
	}

	return &user, nil
}

// GetUserByEmail retrieves a user by their email address
func (s *SQLStore) GetUserByEmail(ctx context.Context, email string) (*models.User, error) {
	query := `
		SELECT id, email, password_hash, created_at, updated_at
		FROM users
		WHERE email = $1`

	var user models.User
	err := s.db.QueryRow(ctx, query, email).Scan(
		&user.ID,
		&user.Email,
		&user.PasswordHash,
		&user.CreatedAt,
		&user.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get user by email: %w", err)
	}

	return &user, nil
}

// UpdateUser updates an existing user
func (s *SQLStore) UpdateUser(ctx context.Context, user *models.User) error {
	query := `
		UPDATE users
		SET email = $2, password_hash = $3, updated_at = $4
		WHERE id = $1`

	_, err := s.db.Exec(ctx, query, user.ID, user.Email, user.PasswordHash, user.UpdatedAt)
	if err != nil {
		return fmt.Errorf("failed to update user: %w", err)
	}

	return nil
}

// CreateUserProfile creates a new user profile
func (s *SQLStore) CreateUserProfile(ctx context.Context, profile *models.UserProfile) error {
	query := `
		INSERT INTO user_profiles (user_id, full_name, age, board, class, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7)`

	_, err := s.db.Exec(ctx, query,
		profile.UserID,
		profile.FullName,
		profile.Age,
		profile.Board,
		profile.Class,
		profile.CreatedAt,
		profile.UpdatedAt,
	)
	if err != nil {
		return fmt.Errorf("failed to create user profile: %w", err)
	}

	return nil
}

// GetUserProfile retrieves a user profile by user ID
func (s *SQLStore) GetUserProfile(ctx context.Context, userID string) (*models.UserProfile, error) {
	query := `
		SELECT user_id, full_name, age, board, class, created_at, updated_at
		FROM user_profiles
		WHERE user_id = $1`

	var profile models.UserProfile
	err := s.db.QueryRow(ctx, query, userID).Scan(
		&profile.UserID,
		&profile.FullName,
		&profile.Age,
		&profile.Board,
		&profile.Class,
		&profile.CreatedAt,
		&profile.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get user profile: %w", err)
	}

	return &profile, nil
}

// UpdateUserProfile updates an existing user profile
func (s *SQLStore) UpdateUserProfile(ctx context.Context, profile *models.UserProfile) error {
	query := `
		UPDATE user_profiles
		SET full_name = $2, age = $3, board = $4, class = $5, updated_at = $6
		WHERE user_id = $1`

	_, err := s.db.Exec(ctx, query,
		profile.UserID,
		profile.FullName,
		profile.Age,
		profile.Board,
		profile.Class,
		profile.UpdatedAt,
	)
	if err != nil {
		return fmt.Errorf("failed to update user profile: %w", err)
	}

	return nil
}

// GetUserWithProfile retrieves a user with their profile information
func (s *SQLStore) GetUserWithProfile(ctx context.Context, userID string) (*models.UserWithProfile, error) {
	query := `
		SELECT 
			u.id, u.email, u.created_at, u.updated_at,
			p.full_name, p.age, p.board, p.class
		FROM users u
		LEFT JOIN user_profiles p ON u.id = p.user_id
		WHERE u.id = $1`

	var userWithProfile models.UserWithProfile
	err := s.db.QueryRow(ctx, query, userID).Scan(
		&userWithProfile.ID,
		&userWithProfile.Email,
		&userWithProfile.CreatedAt,
		&userWithProfile.UpdatedAt,
		&userWithProfile.FullName,
		&userWithProfile.Age,
		&userWithProfile.Board,
		&userWithProfile.Class,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get user with profile: %w", err)
	}

	return &userWithProfile, nil
}
