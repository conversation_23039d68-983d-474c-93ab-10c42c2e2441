package api

import (
	"net/http"
	"time"

	"github.com/jackc/pgx/v5"
	"test-spark-backend/internal/database"
	"test-spark-backend/internal/models"
)

// UserHandler handles user-related HTTP requests
type UserHandler struct {
	store database.Store
}

// NewUserHandler creates a new user handler
func NewUserHandler(store database.Store) *UserHandler {
	return &UserHandler{
		store: store,
	}
}

// GetProfile retrieves the authenticated user's profile
// GET /api/v1/users/me
func (h *UserHandler) GetProfile(w http.ResponseWriter, r *http.Request) {
	// Get user ID from context (set by AuthMiddleware)
	userID, ok := GetUserIDFromContext(r.Context())
	if !ok {
		WriteErrorResponse(w, http.StatusUnauthorized, "User ID not found in context")
		return
	}

	// Get user with profile
	userWithProfile, err := h.store.GetUserWithProfile(r.Context(), userID)
	if err != nil {
		if err == pgx.ErrNoRows {
			WriteErrorResponse(w, http.StatusNotFound, "User not found")
		} else {
			WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get user profile")
		}
		return
	}

	WriteJSONResponse(w, http.StatusOK, userWithProfile)
}

// UpdateProfile updates the authenticated user's profile
// PUT /api/v1/users/me
func (h *UserHandler) UpdateProfile(w http.ResponseWriter, r *http.Request) {
	// Get user ID from context (set by AuthMiddleware)
	userID, ok := GetUserIDFromContext(r.Context())
	if !ok {
		WriteErrorResponse(w, http.StatusUnauthorized, "User ID not found in context")
		return
	}

	var req models.UpdateUserProfileRequest
	if err := ParseJSONBody(r, &req); err != nil {
		WriteErrorResponse(w, http.StatusBadRequest, "Invalid request body: "+err.Error())
		return
	}

	// Get existing profile to check if it exists
	existingProfile, err := h.store.GetUserProfile(r.Context(), userID)
	if err != nil && err != pgx.ErrNoRows {
		WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get existing profile")
		return
	}

	// If profile doesn't exist, create it
	if err == pgx.ErrNoRows {
		profile := &models.UserProfile{
			UserID:    parseUUID(userID),
			FullName:  req.FullName,
			Age:       req.Age,
			Board:     req.Board,
			Class:     req.Class,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}

		if err := h.store.CreateUserProfile(r.Context(), profile); err != nil {
			WriteErrorResponse(w, http.StatusInternalServerError, "Failed to create user profile")
			return
		}
	} else {
		// Update existing profile
		existingProfile.FullName = req.FullName
		existingProfile.Age = req.Age
		existingProfile.Board = req.Board
		existingProfile.Class = req.Class
		existingProfile.UpdatedAt = time.Now()

		if err := h.store.UpdateUserProfile(r.Context(), existingProfile); err != nil {
			WriteErrorResponse(w, http.StatusInternalServerError, "Failed to update user profile")
			return
		}
	}

	// Get updated user with profile
	userWithProfile, err := h.store.GetUserWithProfile(r.Context(), userID)
	if err != nil {
		WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get updated user profile")
		return
	}

	WriteJSONResponse(w, http.StatusOK, userWithProfile)
}
