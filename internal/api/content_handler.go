package api

import (
	"net/http"

	"test-spark-backend/internal/database"
)

// ContentHandler handles content-related HTTP requests
type ContentHand<PERSON> struct {
	store database.Store
}

// NewContentHandler creates a new content handler
func NewContentHandler(store database.Store) *ContentHandler {
	return &ContentHandler{
		store: store,
	}
}

// GetSubjects retrieves all available subjects
// GET /api/v1/content/subjects
func (h *ContentHandler) GetSubjects(w http.ResponseWriter, r *http.Request) {
	subjects, err := h.store.GetSubjects(r.Context())
	if err != nil {
		WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get subjects")
		return
	}

	WriteJSONResponse(w, http.StatusOK, subjects)
}

// GetExams retrieves all available competitive exams
// GET /api/v1/content/exams
func (h *ContentHandler) GetExams(w http.ResponseWriter, r *http.Request) {
	exams, err := h.store.GetExams(r.Context())
	if err != nil {
		WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get exams")
		return
	}

	WriteJSONResponse(w, http.StatusOK, exams)
}

// GetTopicsBySubject retrieves all topics for a specific subject
// GET /api/v1/content/subjects/{subjectId}/topics
func (h *ContentHandler) GetTopicsBySubject(w http.ResponseWriter, r *http.Request) {
	subjectID, err := GetURLParamAsInt(r, "subjectId")
	if err != nil {
		WriteErrorResponse(w, http.StatusBadRequest, "Invalid subject ID: "+err.Error())
		return
	}

	// Verify subject exists
	_, err = h.store.GetSubjectByID(r.Context(), subjectID)
	if err != nil {
		WriteErrorResponse(w, http.StatusNotFound, "Subject not found")
		return
	}

	topics, err := h.store.GetTopicsBySubject(r.Context(), subjectID)
	if err != nil {
		WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get topics")
		return
	}

	WriteJSONResponse(w, http.StatusOK, topics)
}
