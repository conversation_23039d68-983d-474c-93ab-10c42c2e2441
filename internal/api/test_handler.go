package api

import (
	"net/http"

	"test-spark-backend/internal/database"
	"test-spark-backend/internal/models"
	"test-spark-backend/internal/services"

	"github.com/jackc/pgx/v5"
)

// TestHandler handles test-related HTTP requests
type TestHandler struct {
	store       database.Store
	testService *services.TestService
}

// NewTestHandler creates a new test handler
func NewTestHandler(store database.Store, testService *services.TestService) *TestHandler {
	return &TestHandler{
		store:       store,
		testService: testService,
	}
}

// CreateTest creates a new test and returns the first question
// POST /api/v1/tests
func (h *TestHandler) CreateTest(w http.ResponseWriter, r *http.Request) {
	// Get user ID from context
	userID, ok := GetUserIDFromContext(r.Context())
	if !ok {
		WriteErrorResponse(w, http.StatusUnauthorized, "User ID not found in context")
		return
	}

	var req models.CreateTestRequest
	if err := ParseJSONBody(r, &req); err != nil {
		WriteErrorResponse(w, http.StatusBadRequest, "Invalid request body: "+err.Error())
		return
	}

	// Validate required fields
	if req.NumQuestions <= 0 || req.NumQuestions > 50 {
		WriteErrorResponse(w, http.StatusBadRequest, "Number of questions must be between 1 and 50")
		return
	}

	if len(req.DifficultyLevels) == 0 {
		WriteErrorResponse(w, http.StatusBadRequest, "At least one difficulty level is required")
		return
	}

	// Validate difficulty levels
	for _, difficulty := range req.DifficultyLevels {
		if !difficulty.IsValid() {
			WriteErrorResponse(w, http.StatusBadRequest, "Invalid difficulty level: "+string(difficulty))
			return
		}
	}

	// Create test using test service
	testWithFirstQuestion, err := h.testService.CreateNewTest(r.Context(), parseUUID(userID), &req)
	if err != nil {
		WriteErrorResponse(w, http.StatusInternalServerError, "Failed to create test: "+err.Error())
		return
	}

	WriteJSONResponse(w, http.StatusCreated, testWithFirstQuestion)
}

// RegenerateTest creates a new test based on weak areas from a previous test
// POST /api/v1/tests/regenerate
func (h *TestHandler) RegenerateTest(w http.ResponseWriter, r *http.Request) {
	// Get user ID from context
	userID, ok := GetUserIDFromContext(r.Context())
	if !ok {
		WriteErrorResponse(w, http.StatusUnauthorized, "User ID not found in context")
		return
	}

	var req models.RegenerateTestRequest
	if err := ParseJSONBody(r, &req); err != nil {
		WriteErrorResponse(w, http.StatusBadRequest, "Invalid request body: "+err.Error())
		return
	}

	// Validate required fields
	if req.NumQuestions <= 0 || req.NumQuestions > 50 {
		WriteErrorResponse(w, http.StatusBadRequest, "Number of questions must be between 1 and 50")
		return
	}

	// Verify the source test exists and belongs to the user
	sourceTest, err := h.store.GetTestByID(r.Context(), req.SourceTestID.String())
	if err != nil {
		if err == pgx.ErrNoRows {
			WriteErrorResponse(w, http.StatusNotFound, "Source test not found")
		} else {
			WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get source test")
		}
		return
	}

	if sourceTest.UserID.String() != userID {
		WriteErrorResponse(w, http.StatusForbidden, "Source test does not belong to the user")
		return
	}

	// Create regenerated test using test service
	testWithFirstQuestion, err := h.testService.RegenerateForWeakAreas(r.Context(), parseUUID(userID), &req)
	if err != nil {
		WriteErrorResponse(w, http.StatusInternalServerError, "Failed to regenerate test: "+err.Error())
		return
	}

	WriteJSONResponse(w, http.StatusCreated, testWithFirstQuestion)
}

// GetQuestion retrieves a specific question by its order in a test
// GET /api/v1/tests/{testId}/questions/{questionOrder}
func (h *TestHandler) GetQuestion(w http.ResponseWriter, r *http.Request) {
	// Get user ID from context
	userID, ok := GetUserIDFromContext(r.Context())
	if !ok {
		WriteErrorResponse(w, http.StatusUnauthorized, "User ID not found in context")
		return
	}

	// Get URL parameters
	testID := GetURLParam(r, "testId")
	if testID == "" {
		WriteErrorResponse(w, http.StatusBadRequest, "Test ID is required")
		return
	}

	questionOrder, err := GetURLParamAsInt(r, "questionOrder")
	if err != nil {
		WriteErrorResponse(w, http.StatusBadRequest, "Invalid question order: "+err.Error())
		return
	}

	// Verify test exists and belongs to user
	test, err := h.store.GetTestByID(r.Context(), testID)
	if err != nil {
		if err == pgx.ErrNoRows {
			WriteErrorResponse(w, http.StatusNotFound, "Test not found")
		} else {
			WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get test")
		}
		return
	}

	if test.UserID.String() != userID {
		WriteErrorResponse(w, http.StatusForbidden, "Test does not belong to the user")
		return
	}

	// Get the question
	questionWithDetails, err := h.store.GetTestQuestionByOrder(r.Context(), testID, questionOrder)
	if err != nil {
		if err == pgx.ErrNoRows {
			WriteErrorResponse(w, http.StatusNotFound, "Question not found")
		} else {
			WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get question")
		}
		return
	}

	WriteJSONResponse(w, http.StatusOK, questionWithDetails)
}

// SubmitAnswer submits a user's answer to a question
// POST /api/v1/tests/{testId}/answers
func (h *TestHandler) SubmitAnswer(w http.ResponseWriter, r *http.Request) {
	// Get user ID from context
	userID, ok := GetUserIDFromContext(r.Context())
	if !ok {
		WriteErrorResponse(w, http.StatusUnauthorized, "User ID not found in context")
		return
	}

	// Get test ID from URL
	testID := GetURLParam(r, "testId")
	if testID == "" {
		WriteErrorResponse(w, http.StatusBadRequest, "Test ID is required")
		return
	}

	var req models.SubmitAnswerRequest
	if err := ParseJSONBody(r, &req); err != nil {
		WriteErrorResponse(w, http.StatusBadRequest, "Invalid request body: "+err.Error())
		return
	}

	// Verify test exists and belongs to user
	test, err := h.store.GetTestByID(r.Context(), testID)
	if err != nil {
		if err == pgx.ErrNoRows {
			WriteErrorResponse(w, http.StatusNotFound, "Test not found")
		} else {
			WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get test")
		}
		return
	}

	if test.UserID.String() != userID {
		WriteErrorResponse(w, http.StatusForbidden, "Test does not belong to the user")
		return
	}

	// Submit answer using test service
	response, err := h.testService.SubmitAnswer(r.Context(), parseUUID(testID), &req)
	if err != nil {
		WriteErrorResponse(w, http.StatusInternalServerError, "Failed to submit answer: "+err.Error())
		return
	}

	WriteJSONResponse(w, http.StatusOK, response)
}

// CompleteTest marks a test as complete and triggers analysis
// POST /api/v1/tests/{testId}/complete
func (h *TestHandler) CompleteTest(w http.ResponseWriter, r *http.Request) {
	// Get user ID from context
	userID, ok := GetUserIDFromContext(r.Context())
	if !ok {
		WriteErrorResponse(w, http.StatusUnauthorized, "User ID not found in context")
		return
	}

	// Get test ID from URL
	testID := GetURLParam(r, "testId")
	if testID == "" {
		WriteErrorResponse(w, http.StatusBadRequest, "Test ID is required")
		return
	}

	var req models.CompleteTestRequest
	if err := ParseJSONBody(r, &req); err != nil {
		WriteErrorResponse(w, http.StatusBadRequest, "Invalid request body: "+err.Error())
		return
	}

	// Validate time taken
	if req.TimeTakenSeconds <= 0 {
		WriteErrorResponse(w, http.StatusBadRequest, "Time taken must be greater than 0")
		return
	}

	// Verify test exists and belongs to user
	test, err := h.store.GetTestByID(r.Context(), testID)
	if err != nil {
		if err == pgx.ErrNoRows {
			WriteErrorResponse(w, http.StatusNotFound, "Test not found")
		} else {
			WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get test")
		}
		return
	}

	if test.UserID.String() != userID {
		WriteErrorResponse(w, http.StatusForbidden, "Test does not belong to the user")
		return
	}

	// Complete test using test service
	response, err := h.testService.CompleteTest(r.Context(), parseUUID(testID), req.TimeTakenSeconds)
	if err != nil {
		WriteErrorResponse(w, http.StatusInternalServerError, "Failed to complete test: "+err.Error())
		return
	}

	WriteJSONResponse(w, http.StatusOK, response)
}
