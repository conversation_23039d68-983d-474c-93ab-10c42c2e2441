package api

import (
	"net/http"

	"github.com/jackc/pgx/v5"
	"test-spark-backend/internal/database"
	"test-spark-backend/internal/services"
)

// AnalysisHandler handles analysis-related HTTP requests
type AnalysisHandler struct {
	store           database.Store
	analyticsService *services.AnalyticsService
}

// NewAnalysisHandler creates a new analysis handler
func NewAnalysisHandler(store database.Store, analyticsService *services.AnalyticsService) *AnalysisHandler {
	return &AnalysisHandler{
		store:           store,
		analyticsService: analyticsService,
	}
}

// GetTestAnalysis retrieves detailed analysis for a completed test
// GET /api/v1/analysis/tests/{testId}
func (h *AnalysisHandler) GetTestAnalysis(w http.ResponseWriter, r *http.Request) {
	// Get user ID from context
	userID, ok := GetUserIDFromContext(r.Context())
	if !ok {
		WriteErrorResponse(w, http.StatusUnauthorized, "User ID not found in context")
		return
	}

	// Get test ID from URL
	testID := GetURLParam(r, "testId")
	if testID == "" {
		WriteErrorResponse(w, http.StatusBadRequest, "Test ID is required")
		return
	}

	// Verify test exists and belongs to user
	test, err := h.store.GetTestByID(r.Context(), testID)
	if err != nil {
		if err == pgx.ErrNoRows {
			WriteErrorResponse(w, http.StatusNotFound, "Test not found")
		} else {
			WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get test")
		}
		return
	}

	if test.UserID.String() != userID {
		WriteErrorResponse(w, http.StatusForbidden, "Test does not belong to the user")
		return
	}

	// Check if test is completed
	if test.Status != "completed" {
		WriteErrorResponse(w, http.StatusBadRequest, "Test is not completed yet")
		return
	}

	// Get test analysis using analytics service
	analysis, err := h.analyticsService.GetTestAnalysis(r.Context(), parseUUID(testID))
	if err != nil {
		WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get test analysis: "+err.Error())
		return
	}

	WriteJSONResponse(w, http.StatusOK, analysis)
}

// GetDashboard retrieves dashboard summary data for the user
// GET /api/v1/analysis/dashboard
func (h *AnalysisHandler) GetDashboard(w http.ResponseWriter, r *http.Request) {
	// Get user ID from context
	userID, ok := GetUserIDFromContext(r.Context())
	if !ok {
		WriteErrorResponse(w, http.StatusUnauthorized, "User ID not found in context")
		return
	}

	// Get dashboard summary
	dashboard, err := h.store.GetDashboardSummary(r.Context(), userID)
	if err != nil {
		WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get dashboard summary")
		return
	}

	WriteJSONResponse(w, http.StatusOK, dashboard)
}

// GetTopicAnalysis retrieves historical performance data for a specific topic
// GET /api/v1/analysis/topics/{topicId}
func (h *AnalysisHandler) GetTopicAnalysis(w http.ResponseWriter, r *http.Request) {
	// Get user ID from context
	userID, ok := GetUserIDFromContext(r.Context())
	if !ok {
		WriteErrorResponse(w, http.StatusUnauthorized, "User ID not found in context")
		return
	}

	// Get topic ID from URL
	topicID, err := GetURLParamAsInt(r, "topicId")
	if err != nil {
		WriteErrorResponse(w, http.StatusBadRequest, "Invalid topic ID: "+err.Error())
		return
	}

	// Verify topic exists
	_, err = h.store.GetTopicByID(r.Context(), topicID)
	if err != nil {
		if err == pgx.ErrNoRows {
			WriteErrorResponse(w, http.StatusNotFound, "Topic not found")
		} else {
			WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get topic")
		}
		return
	}

	// Get topic performance
	performance, err := h.store.GetTopicPerformance(r.Context(), userID, topicID)
	if err != nil {
		if err == pgx.ErrNoRows {
			WriteErrorResponse(w, http.StatusNotFound, "No performance data found for this topic")
		} else {
			WriteErrorResponse(w, http.StatusInternalServerError, "Failed to get topic performance")
		}
		return
	}

	WriteJSONResponse(w, http.StatusOK, performance)
}
