package api

import (
	"net/http"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
	"test-spark-backend/internal/auth"
	"test-spark-backend/internal/database"
	"test-spark-backend/internal/services"
)

// Router holds all the handlers and dependencies
type Router struct {
	authHandler     *AuthHandler
	userHandler     *UserHandler
	contentHandler  *ContentHandler
	testHandler     *TestHandler
	analysisHandler *AnalysisHandler
	authService     *auth.AuthService
}

// NewRouter creates a new router with all handlers
func NewRouter(
	store database.Store,
	authService *auth.AuthService,
	testService *services.TestService,
	analyticsService *services.AnalyticsService,
) *Router {
	return &Router{
		authHandler:     NewAuthHandler(store, authService),
		userHandler:     NewUserHandler(store),
		contentHandler:  NewContentHandler(store),
		testHandler:     NewTestHandler(store, testService),
		analysisHandler: NewAnalysisHandler(store, analyticsService),
		authService:     authService,
	}
}

// SetupRoutes configures all routes and middleware
func (rt *Router) SetupRoutes() http.Handler {
	r := chi.NewRouter()

	// Global middleware
	r.Use(middleware.Logger)
	r.Use(middleware.Recoverer)
	r.Use(middleware.RequestID)
	r.Use(middleware.RealIP)
	r.Use(CORSMiddleware())
	r.Use(ContentTypeMiddleware())

	// Health check endpoint
	r.Get("/health", func(w http.ResponseWriter, r *http.Request) {
		WriteJSONResponse(w, http.StatusOK, map[string]string{
			"status": "healthy",
			"service": "test-spark-backend",
		})
	})

	// API v1 routes
	r.Route("/api/v1", func(r chi.Router) {
		// Public routes (no authentication required)
		rt.setupPublicRoutes(r)

		// Protected routes (authentication required)
		rt.setupProtectedRoutes(r)
	})

	// 404 handler
	r.NotFound(HandleNotFound)

	// 405 handler
	r.MethodNotAllowed(HandleMethodNotAllowed)

	return r
}

// setupPublicRoutes configures routes that don't require authentication
func (rt *Router) setupPublicRoutes(r chi.Router) {
	// Authentication routes
	r.Route("/auth", func(r chi.Router) {
		r.Post("/register", rt.authHandler.Register)
		r.Post("/login", rt.authHandler.Login)
		r.Post("/refresh-token", rt.authHandler.RefreshToken)
	})

	// Content routes (public access)
	r.Route("/content", func(r chi.Router) {
		r.Get("/subjects", rt.contentHandler.GetSubjects)
		r.Get("/exams", rt.contentHandler.GetExams)
		r.Get("/subjects/{subjectId}/topics", rt.contentHandler.GetTopicsBySubject)
	})
}

// setupProtectedRoutes configures routes that require authentication
func (rt *Router) setupProtectedRoutes(r chi.Router) {
	// Apply authentication middleware to all protected routes
	r.Group(func(r chi.Router) {
		r.Use(AuthMiddleware(rt.authService))

		// Authentication routes that require auth
		r.Route("/auth", func(r chi.Router) {
			r.Post("/logout", rt.authHandler.Logout)
		})

		// User profile routes
		r.Route("/users", func(r chi.Router) {
			r.Get("/me", rt.userHandler.GetProfile)
			r.Put("/me", rt.userHandler.UpdateProfile)
		})

		// Test routes
		r.Route("/tests", func(r chi.Router) {
			r.Post("/", rt.testHandler.CreateTest)
			r.Post("/regenerate", rt.testHandler.RegenerateTest)
			
			r.Route("/{testId}", func(r chi.Router) {
				r.Get("/questions/{questionOrder}", rt.testHandler.GetQuestion)
				r.Post("/answers", rt.testHandler.SubmitAnswer)
				r.Post("/complete", rt.testHandler.CompleteTest)
			})
		})

		// Analysis routes
		r.Route("/analysis", func(r chi.Router) {
			r.Get("/dashboard", rt.analysisHandler.GetDashboard)
			r.Get("/tests/{testId}", rt.analysisHandler.GetTestAnalysis)
			r.Get("/topics/{topicId}", rt.analysisHandler.GetTopicAnalysis)
		})
	})
}
