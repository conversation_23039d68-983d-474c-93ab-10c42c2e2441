package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"

	"github.com/go-chi/chi/v5"
)

// SuccessResponse represents a standard success response
type SuccessResponse struct {
	Data    interface{} `json:"data,omitempty"`
	Message string      `json:"message,omitempty"`
	Code    int         `json:"code"`
}

// WriteJSONResponse writes a JSON response with the given status code and data
func WriteJSONResponse(w http.ResponseWriter, statusCode int, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	if data != nil {
		if err := json.NewEncoder(w).Encode(data); err != nil {
			// If encoding fails, write a simple error response
			http.Error(w, "Internal server error", http.StatusInternalServerError)
		}
	}
}

// WriteSuccessResponse writes a standardized success response
func WriteSuccessResponse(w http.ResponseWriter, statusCode int, data interface{}, message string) {
	response := SuccessResponse{
		Data:    data,
		Message: message,
		Code:    statusCode,
	}
	WriteJSONResponse(w, statusCode, response)
}

// WriteErrorResponse writes a standardized error response (exported version)
func WriteErrorResponse(w http.ResponseWriter, statusCode int, message string) {
	writeErrorResponse(w, statusCode, message)
}

// ParseJSONBody parses JSON request body into the provided struct
func ParseJSONBody(r *http.Request, dest interface{}) error {
	if r.Body == nil {
		return fmt.Errorf("request body is empty")
	}

	decoder := json.NewDecoder(r.Body)
	decoder.DisallowUnknownFields() // Strict parsing

	if err := decoder.Decode(dest); err != nil {
		return fmt.Errorf("invalid JSON: %w", err)
	}

	return nil
}

// GetURLParam extracts a URL parameter from chi router
func GetURLParam(r *http.Request, key string) string {
	return chi.URLParam(r, key)
}

// GetURLParamAsInt extracts a URL parameter and converts it to int
func GetURLParamAsInt(r *http.Request, key string) (int, error) {
	param := chi.URLParam(r, key)
	if param == "" {
		return 0, fmt.Errorf("parameter %s is required", key)
	}

	value, err := strconv.Atoi(param)
	if err != nil {
		return 0, fmt.Errorf("parameter %s must be a valid integer", key)
	}

	return value, nil
}

// GetQueryParam extracts a query parameter
func GetQueryParam(r *http.Request, key string) string {
	return r.URL.Query().Get(key)
}

// GetQueryParamAsInt extracts a query parameter and converts it to int
func GetQueryParamAsInt(r *http.Request, key string, defaultValue int) int {
	param := r.URL.Query().Get(key)
	if param == "" {
		return defaultValue
	}

	value, err := strconv.Atoi(param)
	if err != nil {
		return defaultValue
	}

	return value
}

// ValidateRequiredFields checks if required fields are present and non-empty
func ValidateRequiredFields(fields map[string]interface{}) error {
	for fieldName, fieldValue := range fields {
		if fieldValue == nil {
			return fmt.Errorf("field %s is required", fieldName)
		}

		// Check for empty strings
		if str, ok := fieldValue.(string); ok && str == "" {
			return fmt.Errorf("field %s cannot be empty", fieldName)
		}

		// Check for zero values for pointers
		if ptr, ok := fieldValue.(*string); ok && (ptr == nil || *ptr == "") {
			return fmt.Errorf("field %s is required", fieldName)
		}
	}

	return nil
}

// HandleMethodNotAllowed returns a 405 Method Not Allowed response
func HandleMethodNotAllowed(w http.ResponseWriter, r *http.Request) {
	WriteErrorResponse(w, http.StatusMethodNotAllowed, fmt.Sprintf("Method %s not allowed", r.Method))
}

// HandleNotFound returns a 404 Not Found response
func HandleNotFound(w http.ResponseWriter, r *http.Request) {
	WriteErrorResponse(w, http.StatusNotFound, "Resource not found")
}
