package config

import (
	"fmt"
	"os"
	"strconv"

	"github.com/joho/godotenv"
)

// Config holds all configuration for the application
type Config struct {
	DatabaseURL              string
	JWTSecret               string
	JWTAccessTokenExpMinutes int
	JWTRefreshTokenExpDays   int
	GroqAPIKey              string
	Port                    string
}

// Load reads configuration from environment variables and .env file
func Load() (*Config, error) {
	// Load .env file if it exists (ignore error if file doesn't exist)
	_ = godotenv.Load()

	config := &Config{}

	// Required environment variables
	config.DatabaseURL = os.Getenv("DATABASE_URL")
	if config.DatabaseURL == "" {
		return nil, fmt.Errorf("DATABASE_URL environment variable is required")
	}

	config.JWTSecret = os.Getenv("JWT_SECRET")
	if config.JWTSecret == "" {
		return nil, fmt.Errorf("JWT_SECRET environment variable is required")
	}

	config.GroqAPIKey = os.Getenv("GROQ_API_KEY")
	if config.GroqAPIKey == "" {
		return nil, fmt.Errorf("GROQ_API_KEY environment variable is required")
	}

	// JWT Access Token Expiry (default: 15 minutes)
	accessTokenExpStr := os.Getenv("JWT_ACCESS_TOKEN_EXP_MINUTES")
	if accessTokenExpStr == "" {
		config.JWTAccessTokenExpMinutes = 15
	} else {
		exp, err := strconv.Atoi(accessTokenExpStr)
		if err != nil {
			return nil, fmt.Errorf("invalid JWT_ACCESS_TOKEN_EXP_MINUTES: %v", err)
		}
		config.JWTAccessTokenExpMinutes = exp
	}

	// JWT Refresh Token Expiry (default: 7 days)
	refreshTokenExpStr := os.Getenv("JWT_REFRESH_TOKEN_EXP_DAYS")
	if refreshTokenExpStr == "" {
		config.JWTRefreshTokenExpDays = 7
	} else {
		exp, err := strconv.Atoi(refreshTokenExpStr)
		if err != nil {
			return nil, fmt.Errorf("invalid JWT_REFRESH_TOKEN_EXP_DAYS: %v", err)
		}
		config.JWTRefreshTokenExpDays = exp
	}

	// Server Port (default: 8080)
	config.Port = os.Getenv("PORT")
	if config.Port == "" {
		config.Port = "8080"
	}

	return config, nil
}
