package models

import (
	"time"

	"github.com/google/uuid"
)

// User represents the users table
type User struct {
	ID           uuid.UUID `json:"id" db:"id"`
	Email        string    `json:"email" db:"email"`
	PasswordHash string    `json:"-" db:"password_hash"` // Never include in JSON responses
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time `json:"updated_at" db:"updated_at"`
}

// UserProfile represents the user_profiles table
type UserProfile struct {
	UserID    uuid.UUID  `json:"user_id" db:"user_id"`
	FullName  *string    `json:"full_name" db:"full_name"`
	Age       *int       `json:"age" db:"age"`
	Board     *string    `json:"board" db:"board"`
	Class     *string    `json:"class" db:"class"`
	CreatedAt time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt time.Time  `json:"updated_at" db:"updated_at"`
}

// RefreshToken represents the refresh_tokens table
type RefreshToken struct {
	ID        uuid.UUID `json:"id" db:"id"`
	UserID    uuid.UUID `json:"user_id" db:"user_id"`
	TokenHash string    `json:"-" db:"token_hash"` // Never include in JSON responses
	ExpiresAt time.Time `json:"expires_at" db:"expires_at"`
	IsRevoked bool      `json:"is_revoked" db:"is_revoked"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
}

// UserWithProfile combines User and UserProfile for API responses
type UserWithProfile struct {
	ID        uuid.UUID  `json:"user_id"`
	Email     string     `json:"email"`
	FullName  *string    `json:"full_name"`
	Age       *int       `json:"age"`
	Board     *string    `json:"board"`
	Class     *string    `json:"class"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
}

// CreateUserRequest represents the request payload for user registration
type CreateUserRequest struct {
	Email    string  `json:"email" validate:"required,email"`
	Password string  `json:"password" validate:"required,min=8"`
	FullName *string `json:"full_name"`
	Age      *int    `json:"age"`
	Board    *string `json:"board"`
	Class    *string `json:"class"`
}

// UpdateUserProfileRequest represents the request payload for updating user profile
type UpdateUserProfileRequest struct {
	FullName *string `json:"full_name"`
	Age      *int    `json:"age"`
	Board    *string `json:"board"`
	Class    *string `json:"class"`
}
