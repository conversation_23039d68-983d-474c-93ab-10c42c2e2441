package models

// DifficultyLevel represents the difficulty_level enum from the database
type DifficultyLevel string

const (
	DifficultyEasy   DifficultyLevel = "easy"
	DifficultyMedium DifficultyLevel = "medium"
	DifficultyHard   DifficultyLevel = "hard"
)

// TestStatus represents the test_status enum from the database
type TestStatus string

const (
	TestStatusPending    TestStatus = "pending"
	TestStatusInProgress TestStatus = "in_progress"
	TestStatusCompleted  TestStatus = "completed"
	TestStatusCancelled  TestStatus = "cancelled"
)

// IsValid checks if the difficulty level is valid
func (d DifficultyLevel) IsValid() bool {
	switch d {
	case DifficultyEasy, DifficultyMedium, DifficultyHard:
		return true
	default:
		return false
	}
}

// IsValid checks if the test status is valid
func (t TestStatus) IsValid() bool {
	switch t {
	case TestStatusPending, TestStatusInProgress, TestStatusCompleted, TestStatusCancelled:
		return true
	default:
		return false
	}
}
