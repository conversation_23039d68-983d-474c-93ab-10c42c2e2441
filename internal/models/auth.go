package models

import "github.com/google/uuid"

// LoginRequest represents the request payload for user login
type LoginRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required"`
}

// LoginResponse represents the response after successful login
type LoginResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
}

// RefreshTokenRequest represents the request payload for refreshing access token
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" validate:"required"`
}

// RefreshTokenResponse represents the response after refreshing access token
type RefreshTokenResponse struct {
	AccessToken string `json:"access_token"`
}

// LogoutRequest represents the request payload for user logout
type LogoutRequest struct {
	RefreshToken string `json:"refresh_token" validate:"required"`
}

// JWTClaims represents the claims stored in JWT tokens
type JWTClaims struct {
	UserID uuid.UUID `json:"sub"`
	Email  string    `json:"email"`
	Exp    int64     `json:"exp"`
	Iat    int64     `json:"iat"`
}

// RegisterResponse represents the response after successful registration
type RegisterResponse struct {
	UserID   uuid.UUID `json:"user_id"`
	Email    string    `json:"email"`
	FullName *string   `json:"full_name"`
}
