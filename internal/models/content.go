package models

import "github.com/google/uuid"

// Subject represents the subjects table
type Subject struct {
	ID          int     `json:"id" db:"id"`
	Name        string  `json:"name" db:"name"`
	Description *string `json:"description" db:"description"`
}

// Exam represents the exams table
type Exam struct {
	ID          int     `json:"id" db:"id"`
	Name        string  `json:"name" db:"name"`
	Description *string `json:"description" db:"description"`
}

// Topic represents the topics table
type Topic struct {
	ID          int     `json:"id" db:"id"`
	SubjectID   int     `json:"subject_id" db:"subject_id"`
	Name        string  `json:"name" db:"name"`
	Description *string `json:"description" db:"description"`
}

// UserPreference represents the user_preferences table
type UserPreference struct {
	UserID    uuid.UUID `json:"user_id" db:"user_id"`
	SubjectID *int      `json:"subject_id" db:"subject_id"`
	ExamID    *int      `json:"exam_id" db:"exam_id"`
}

// TopicWithSubject combines Topic with Subject information for API responses
type TopicWithSubject struct {
	ID            int     `json:"id"`
	SubjectID     int     `json:"subject_id"`
	SubjectName   string  `json:"subject_name"`
	Name          string  `json:"name"`
	Description   *string `json:"description"`
}
