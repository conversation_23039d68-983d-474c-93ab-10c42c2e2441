package models

import (
	"time"

	"github.com/google/uuid"
)

// Test represents the tests table
type Test struct {
	ID                   uuid.UUID  `json:"id" db:"id"`
	UserID               uuid.UUID  `json:"user_id" db:"user_id"`
	TestContextSubjectID *int       `json:"test_context_subject_id" db:"test_context_subject_id"`
	TestContextExamID    *int       `json:"test_context_exam_id" db:"test_context_exam_id"`
	Status               TestStatus `json:"status" db:"status"`
	CreatedAt            time.Time  `json:"created_at" db:"created_at"`
	CompletedAt          *time.Time `json:"completed_at" db:"completed_at"`
}

// TestQuestion represents the test_questions table
type TestQuestion struct {
	ID            int       `json:"id" db:"id"`
	TestID        uuid.UUID `json:"test_id" db:"test_id"`
	QuestionID    uuid.UUID `json:"question_id" db:"question_id"`
	QuestionOrder int       `json:"question_order" db:"question_order"`
}

// UserAnswer represents the user_answers table
type UserAnswer struct {
	ID                  int       `json:"id" db:"id"`
	TestQuestionID      int       `json:"test_question_id" db:"test_question_id"`
	SelectedOptionIndex int       `json:"selected_option_index" db:"selected_option_index"`
	IsCorrect           bool      `json:"is_correct" db:"is_correct"`
	AnsweredAt          time.Time `json:"answered_at" db:"answered_at"`
}

// TestWithContext combines Test with Subject and Exam information
type TestWithContext struct {
	ID          uuid.UUID  `json:"id"`
	UserID      uuid.UUID  `json:"user_id"`
	SubjectID   *int       `json:"subject_id"`
	SubjectName *string    `json:"subject_name"`
	ExamID      *int       `json:"exam_id"`
	ExamName    *string    `json:"exam_name"`
	Status      TestStatus `json:"status"`
	CreatedAt   time.Time  `json:"created_at"`
	CompletedAt *time.Time `json:"completed_at"`
}

// CreateTestRequest represents the request payload for creating a new test
type CreateTestRequest struct {
	SubjectID        *int              `json:"subject_id"`
	ExamID           *int              `json:"exam_id"`
	NumQuestions     int               `json:"num_questions" validate:"required,min=1,max=50"`
	DifficultyLevels []DifficultyLevel `json:"difficulty_levels" validate:"required,min=1"`
	TopicIDs         []int             `json:"topic_ids"` // Optional: specific topics to focus on
}

// RegenerateTestRequest represents the request payload for regenerating a test based on weak areas
type RegenerateTestRequest struct {
	SourceTestID uuid.UUID `json:"source_test_id" validate:"required"`
	NumQuestions int       `json:"num_questions" validate:"required,min=1,max=50"`
}

// SubmitAnswerRequest represents the request payload for submitting an answer
type SubmitAnswerRequest struct {
	QuestionID          uuid.UUID `json:"question_id" validate:"required"`
	SelectedOptionIndex int       `json:"selected_option_index" validate:"required,min=0"`
}

// SubmitAnswerResponse represents the response after submitting an answer
type SubmitAnswerResponse struct {
	IsCorrect          bool   `json:"is_correct"`
	CorrectOptionIndex int    `json:"correct_option_index"`
	Explanation        string `json:"explanation"`
}

// TestQuestionWithDetails combines TestQuestion with Question and Topic details
type TestQuestionWithDetails struct {
	ID            int             `json:"id"`
	TestID        uuid.UUID       `json:"test_id"`
	QuestionID    uuid.UUID       `json:"question_id"`
	QuestionOrder int             `json:"question_order"`
	Question      QuestionContent `json:"question"`
	TopicName     string          `json:"topic_name"`
	Difficulty    *DifficultyLevel `json:"difficulty"`
}
