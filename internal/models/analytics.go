package models

import (
	"time"

	"github.com/google/uuid"
)

// TestResult represents the test_results table
type TestResult struct {
	ID               uuid.UUID `json:"id" db:"id"`
	TestID           uuid.UUID `json:"test_id" db:"test_id"`
	UserID           uuid.UUID `json:"user_id" db:"user_id"`
	Score            float64   `json:"score" db:"score"`
	TotalQuestions   int       `json:"total_questions" db:"total_questions"`
	CorrectAnswers   int       `json:"correct_answers" db:"correct_answers"`
	IncorrectAnswers int       `json:"incorrect_answers" db:"incorrect_answers"`
	TimeTakenSeconds int       `json:"time_taken_seconds" db:"time_taken_seconds"`
}

// TopicPerformanceSummary represents the topic_performance_summary table
type TopicPerformanceSummary struct {
	ID               int        `json:"id" db:"id"`
	UserID           uuid.UUID  `json:"user_id" db:"user_id"`
	TopicID          int        `json:"topic_id" db:"topic_id"`
	TotalAttempted   int        `json:"total_attempted" db:"total_attempted"`
	TotalCorrect     int        `json:"total_correct" db:"total_correct"`
	ProficiencyScore float64    `json:"proficiency_score" db:"proficiency_score"`
	LastTestedAt     *time.Time `json:"last_tested_at" db:"last_tested_at"`
}

// UserTestHistory represents the v_user_test_history view
type UserTestHistory struct {
	TestID           uuid.UUID  `json:"test_id" db:"test_id"`
	UserID           uuid.UUID  `json:"user_id" db:"user_id"`
	Status           TestStatus `json:"status" db:"status"`
	CreatedAt        time.Time  `json:"created_at" db:"created_at"`
	CompletedAt      *time.Time `json:"completed_at" db:"completed_at"`
	SubjectName      *string    `json:"subject_name" db:"subject_name"`
	ExamName         *string    `json:"exam_name" db:"exam_name"`
	Score            *float64   `json:"score" db:"score"`
	TotalQuestions   *int       `json:"total_questions" db:"total_questions"`
	CorrectAnswers   *int       `json:"correct_answers" db:"correct_answers"`
	IncorrectAnswers *int       `json:"incorrect_answers" db:"incorrect_answers"`
	TimeTakenSeconds *int       `json:"time_taken_seconds" db:"time_taken_seconds"`
}

// TestAnalysis represents detailed analysis of a completed test
type TestAnalysis struct {
	TestID           uuid.UUID                    `json:"test_id"`
	Score            float64                      `json:"score"`
	TotalQuestions   int                          `json:"total_questions"`
	CorrectAnswers   int                          `json:"correct_answers"`
	IncorrectAnswers int                          `json:"incorrect_answers"`
	TimeTakenSeconds int                          `json:"time_taken_seconds"`
	StrongTopics     []TopicPerformanceDetail     `json:"strong_topics"`
	WeakTopics       []TopicPerformanceDetail     `json:"weak_topics"`
	QuestionBreakdown []QuestionAnalysisDetail    `json:"question_breakdown"`
}

// TopicPerformanceDetail represents performance details for a specific topic
type TopicPerformanceDetail struct {
	TopicID          int     `json:"topic_id"`
	TopicName        string  `json:"topic_name"`
	TotalQuestions   int     `json:"total_questions"`
	CorrectAnswers   int     `json:"correct_answers"`
	AccuracyRate     float64 `json:"accuracy_rate"`
	ProficiencyScore float64 `json:"proficiency_score"`
}

// QuestionAnalysisDetail represents analysis for a specific question in a test
type QuestionAnalysisDetail struct {
	QuestionID          uuid.UUID       `json:"question_id"`
	QuestionOrder       int             `json:"question_order"`
	TopicName           string          `json:"topic_name"`
	Difficulty          *DifficultyLevel `json:"difficulty"`
	UserAnswer          int             `json:"user_answer"`
	CorrectAnswer       int             `json:"correct_answer"`
	IsCorrect           bool            `json:"is_correct"`
	Question            string          `json:"question"`
	Options             []string        `json:"options"`
	Explanation         string          `json:"explanation"`
}

// DashboardSummary represents high-level summary data for the user dashboard
type DashboardSummary struct {
	OverallProficiency float64           `json:"overall_proficiency"`
	RecentTests        []UserTestHistory `json:"recent_tests"`
	StrongestSubject   *string           `json:"strongest_subject"`
	WeakestSubject     *string           `json:"weakest_subject"`
	TotalTestsTaken    int               `json:"total_tests_taken"`
	AverageScore       float64           `json:"average_score"`
	TopicPerformance   []TopicPerformanceWithName `json:"topic_performance"`
}

// TopicPerformanceWithName combines TopicPerformanceSummary with topic name
type TopicPerformanceWithName struct {
	TopicID          int        `json:"topic_id"`
	TopicName        string     `json:"topic_name"`
	SubjectName      string     `json:"subject_name"`
	TotalAttempted   int        `json:"total_attempted"`
	TotalCorrect     int        `json:"total_correct"`
	ProficiencyScore float64    `json:"proficiency_score"`
	LastTestedAt     *time.Time `json:"last_tested_at"`
}

// CompleteTestRequest represents the request payload for completing a test
type CompleteTestRequest struct {
	TimeTakenSeconds int `json:"time_taken_seconds" validate:"required,min=1"`
}

// CompleteTestResponse represents the response after completing a test
type CompleteTestResponse struct {
	TestID      uuid.UUID `json:"test_id"`
	Message     string    `json:"message"`
	AnalysisURL string    `json:"analysis_url"`
}
