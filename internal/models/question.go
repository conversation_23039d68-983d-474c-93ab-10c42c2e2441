package models

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

// Question represents the questions table
type Question struct {
	ID            uuid.UUID        `json:"id" db:"id"`
	TopicID       int              `json:"topic_id" db:"topic_id"`
	Content       json.RawMessage  `json:"content" db:"content"` // JSONB field
	Difficulty    *DifficultyLevel `json:"difficulty" db:"difficulty"`
	AuthorAIModel *string          `json:"author_ai_model" db:"author_ai_model"`
	CreatedAt     time.Time        `json:"created_at" db:"created_at"`
}

// QuestionContent represents the structure of the content JSONB field
type QuestionContent struct {
	Question           string   `json:"question"`
	Options            []string `json:"options"`
	CorrectOptionIndex int      `json:"correct_option_index"`
	Explanation        string   `json:"explanation"`
}

// QuestionWithTopic combines Question with Topic information for API responses
type QuestionWithTopic struct {
	ID            uuid.UUID        `json:"id"`
	TopicID       int              `json:"topic_id"`
	TopicName     string           `json:"topic_name"`
	Content       QuestionContent  `json:"content"`
	Difficulty    *DifficultyLevel `json:"difficulty"`
	AuthorAIModel *string          `json:"author_ai_model"`
	CreatedAt     time.Time        `json:"created_at"`
}

// CreateQuestionRequest represents the request for creating a new question
type CreateQuestionRequest struct {
	TopicID       int              `json:"topic_id" validate:"required"`
	Content       QuestionContent  `json:"content" validate:"required"`
	Difficulty    *DifficultyLevel `json:"difficulty"`
	AuthorAIModel *string          `json:"author_ai_model"`
}

// GetContent parses the JSONB content field into QuestionContent struct
func (q *Question) GetContent() (*QuestionContent, error) {
	var content QuestionContent
	err := json.Unmarshal(q.Content, &content)
	if err != nil {
		return nil, err
	}
	return &content, nil
}

// SetContent sets the content field from QuestionContent struct
func (q *Question) SetContent(content QuestionContent) error {
	contentBytes, err := json.Marshal(content)
	if err != nil {
		return err
	}
	q.Content = contentBytes
	return nil
}

// UnmarshalJSON implements json.Unmarshaler for QuestionContent
func (qc *QuestionContent) UnmarshalJSON(data []byte) error {
	return json.Unmarshal(data, (*struct {
		Question           string   `json:"question"`
		Options            []string `json:"options"`
		CorrectOptionIndex int      `json:"correct_option_index"`
		Explanation        string   `json:"explanation"`
	})(qc))
}
