package auth

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
	"test-spark-backend/internal/config"
	"test-spark-backend/internal/models"
)

// AuthService handles authentication operations
type AuthService struct {
	config *config.Config
}

// NewAuthService creates a new authentication service
func NewAuthService(cfg *config.Config) *AuthService {
	return &AuthService{
		config: cfg,
	}
}

// HashPassword hashes a password using bcrypt
func (a *AuthService) HashPassword(password string) (string, error) {
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", fmt.Errorf("failed to hash password: %w", err)
	}
	return string(hashedBytes), nil
}

// ComparePassword compares a plaintext password with a hashed password
func (a *AuthService) ComparePassword(hashedPassword, password string) error {
	err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
	if err != nil {
		return fmt.Errorf("password comparison failed: %w", err)
	}
	return nil
}

// GenerateAccessToken generates a new JWT access token
func (a *AuthService) GenerateAccessToken(userID uuid.UUID, email string) (string, error) {
	now := time.Now()
	expirationTime := now.Add(time.Duration(a.config.JWTAccessTokenExpMinutes) * time.Minute)

	claims := &models.JWTClaims{
		UserID: userID,
		Email:  email,
		Exp:    expirationTime.Unix(),
		Iat:    now.Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(a.config.JWTSecret))
	if err != nil {
		return "", fmt.Errorf("failed to sign access token: %w", err)
	}

	return tokenString, nil
}

// GenerateRefreshToken generates a new refresh token
func (a *AuthService) GenerateRefreshToken() (string, error) {
	// Generate a random 32-byte token
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.Errorf("failed to generate refresh token: %w", err)
	}
	return hex.EncodeToString(bytes), nil
}

// HashRefreshToken hashes a refresh token for storage
func (a *AuthService) HashRefreshToken(token string) (string, error) {
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(token), bcrypt.DefaultCost)
	if err != nil {
		return "", fmt.Errorf("failed to hash refresh token: %w", err)
	}
	return string(hashedBytes), nil
}

// CompareRefreshToken compares a plaintext refresh token with a hashed token
func (a *AuthService) CompareRefreshToken(hashedToken, token string) error {
	err := bcrypt.CompareHashAndPassword([]byte(hashedToken), []byte(token))
	if err != nil {
		return fmt.Errorf("refresh token comparison failed: %w", err)
	}
	return nil
}

// ValidateAccessToken validates and parses a JWT access token
func (a *AuthService) ValidateAccessToken(tokenString string) (*models.JWTClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &models.JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Verify the signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(a.config.JWTSecret), nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	if !token.Valid {
		return nil, fmt.Errorf("invalid token")
	}

	claims, ok := token.Claims.(*models.JWTClaims)
	if !ok {
		return nil, fmt.Errorf("invalid token claims")
	}

	// Check if token is expired
	if time.Now().Unix() > claims.Exp {
		return nil, fmt.Errorf("token has expired")
	}

	return claims, nil
}

// CreateRefreshTokenModel creates a refresh token model for database storage
func (a *AuthService) CreateRefreshTokenModel(userID uuid.UUID, token string) (*models.RefreshToken, error) {
	hashedToken, err := a.HashRefreshToken(token)
	if err != nil {
		return nil, err
	}

	expirationTime := time.Now().Add(time.Duration(a.config.JWTRefreshTokenExpDays) * 24 * time.Hour)

	refreshToken := &models.RefreshToken{
		ID:        uuid.New(),
		UserID:    userID,
		TokenHash: hashedToken,
		ExpiresAt: expirationTime,
		IsRevoked: false,
		CreatedAt: time.Now(),
	}

	return refreshToken, nil
}

// IsRefreshTokenExpired checks if a refresh token is expired
func (a *AuthService) IsRefreshTokenExpired(token *models.RefreshToken) bool {
	return time.Now().After(token.ExpiresAt)
}

// ExtractUserIDFromToken extracts user ID from a valid JWT token string
func (a *AuthService) ExtractUserIDFromToken(tokenString string) (uuid.UUID, error) {
	claims, err := a.ValidateAccessToken(tokenString)
	if err != nil {
		return uuid.Nil, err
	}
	return claims.UserID, nil
}
