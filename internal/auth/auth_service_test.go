package auth

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"test-spark-backend/internal/config"
)

func TestAuthService(t *testing.T) {
	// Create a test config
	cfg := &config.Config{
		JWTSecret:               "test-secret-key",
		JWTAccessTokenExpMinutes: 15,
		JWTRefreshTokenExpDays:   7,
	}

	authService := NewAuthService(cfg)

	t.Run("HashPassword", func(t *testing.T) {
		password := "testpassword123"
		hashedPassword, err := authService.HashPassword(password)
		if err != nil {
			t.Fatalf("Failed to hash password: %v", err)
		}

		if hashedPassword == "" {
			t.Fatal("Hashed password should not be empty")
		}

		if hashedPassword == password {
			t.Fatal("Hashed password should not equal original password")
		}
	})

	t.Run("ComparePassword", func(t *testing.T) {
		password := "testpassword123"
		hashedPassword, err := authService.HashPassword(password)
		if err != nil {
			t.Fatalf("Failed to hash password: %v", err)
		}

		// Test correct password
		err = authService.ComparePassword(hashedPassword, password)
		if err != nil {
			t.Fatalf("Password comparison should succeed: %v", err)
		}

		// Test incorrect password
		err = authService.ComparePassword(hashedPassword, "wrongpassword")
		if err == nil {
			t.Fatal("Password comparison should fail for wrong password")
		}
	})

	t.Run("GenerateAndValidateAccessToken", func(t *testing.T) {
		userID := uuid.New()
		email := "<EMAIL>"

		// Generate token
		token, err := authService.GenerateAccessToken(userID, email)
		if err != nil {
			t.Fatalf("Failed to generate access token: %v", err)
		}

		if token == "" {
			t.Fatal("Generated token should not be empty")
		}

		// Validate token
		claims, err := authService.ValidateAccessToken(token)
		if err != nil {
			t.Fatalf("Failed to validate access token: %v", err)
		}

		if claims.UserID != userID {
			t.Fatalf("Expected user ID %v, got %v", userID, claims.UserID)
		}

		if claims.Email != email {
			t.Fatalf("Expected email %s, got %s", email, claims.Email)
		}

		// Check expiration time
		expectedExp := time.Now().Add(time.Duration(cfg.JWTAccessTokenExpMinutes) * time.Minute).Unix()
		if claims.Exp < expectedExp-5 || claims.Exp > expectedExp+5 { // Allow 5 second tolerance
			t.Fatalf("Token expiration time is not as expected")
		}
	})

	t.Run("GenerateRefreshToken", func(t *testing.T) {
		token, err := authService.GenerateRefreshToken()
		if err != nil {
			t.Fatalf("Failed to generate refresh token: %v", err)
		}

		if token == "" {
			t.Fatal("Generated refresh token should not be empty")
		}

		if len(token) != 64 { // 32 bytes = 64 hex characters
			t.Fatalf("Expected refresh token length 64, got %d", len(token))
		}
	})

	t.Run("HashAndCompareRefreshToken", func(t *testing.T) {
		token, err := authService.GenerateRefreshToken()
		if err != nil {
			t.Fatalf("Failed to generate refresh token: %v", err)
		}

		hashedToken, err := authService.HashRefreshToken(token)
		if err != nil {
			t.Fatalf("Failed to hash refresh token: %v", err)
		}

		// Test correct token
		err = authService.CompareRefreshToken(hashedToken, token)
		if err != nil {
			t.Fatalf("Refresh token comparison should succeed: %v", err)
		}

		// Test incorrect token
		wrongToken, _ := authService.GenerateRefreshToken()
		err = authService.CompareRefreshToken(hashedToken, wrongToken)
		if err == nil {
			t.Fatal("Refresh token comparison should fail for wrong token")
		}
	})

	t.Run("CreateRefreshTokenModel", func(t *testing.T) {
		userID := uuid.New()
		token, err := authService.GenerateRefreshToken()
		if err != nil {
			t.Fatalf("Failed to generate refresh token: %v", err)
		}

		refreshTokenModel, err := authService.CreateRefreshTokenModel(userID, token)
		if err != nil {
			t.Fatalf("Failed to create refresh token model: %v", err)
		}

		if refreshTokenModel.UserID != userID {
			t.Fatalf("Expected user ID %v, got %v", userID, refreshTokenModel.UserID)
		}

		if refreshTokenModel.IsRevoked {
			t.Fatal("New refresh token should not be revoked")
		}

		// Check expiration time
		expectedExp := time.Now().Add(time.Duration(cfg.JWTRefreshTokenExpDays) * 24 * time.Hour)
		if refreshTokenModel.ExpiresAt.Before(expectedExp.Add(-time.Minute)) ||
			refreshTokenModel.ExpiresAt.After(expectedExp.Add(time.Minute)) {
			t.Fatal("Refresh token expiration time is not as expected")
		}
	})

	t.Run("ExtractUserIDFromToken", func(t *testing.T) {
		userID := uuid.New()
		email := "<EMAIL>"

		token, err := authService.GenerateAccessToken(userID, email)
		if err != nil {
			t.Fatalf("Failed to generate access token: %v", err)
		}

		extractedUserID, err := authService.ExtractUserIDFromToken(token)
		if err != nil {
			t.Fatalf("Failed to extract user ID from token: %v", err)
		}

		if extractedUserID != userID {
			t.Fatalf("Expected user ID %v, got %v", userID, extractedUserID)
		}
	})
}
