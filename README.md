# Test-Spark Backend

A Go-based backend for the Test-Spark adaptive learning platform.

## Features

- RESTful API with JWT authentication
- PostgreSQL database with pgx driver
- Adaptive test generation using Groq AI
- Performance analytics and reporting
- Repository pattern for data access

## Project Structure

```
├── cmd/api/              # Main application entry point
├── internal/
│   ├── api/             # HTTP handlers, router, and middleware
│   ├── auth/            # JWT generation and validation service
│   ├── config/          # Configuration loading from environment
│   ├── database/        # Repository pattern implementation
│   ├── models/          # Go structs for database tables
│   └── services/        # Business logic (TestService, GroqClient)
├── frontend/            # Frontend application (separate)
├── schema.sql           # Database schema
└── apilist.md          # API specification
```

## Prerequisites

- Go 1.21 or later
- PostgreSQL 12 or later
- Groq API key

## Setup

1. Clone the repository
2. Copy `.env.example` to `.env` and fill in your configuration:
   ```bash
   cp .env.example .env
   ```

3. Set up your PostgreSQL database and run the schema:
   ```bash
   psql -d your_database -f schema.sql
   ```

4. Install dependencies:
   ```bash
   go mod download
   ```

5. Run the application:
   ```bash
   go run cmd/api/main.go
   ```

## Environment Variables

- `DATABASE_URL`: PostgreSQL connection string
- `JWT_SECRET`: Secret key for JWT token signing
- `JWT_ACCESS_TOKEN_EXP_MINUTES`: Access token expiry in minutes (default: 15)
- `JWT_REFRESH_TOKEN_EXP_DAYS`: Refresh token expiry in days (default: 7)
- `GROQ_API_KEY`: API key for Groq AI service
- `PORT`: Server port (default: 8080)

## API Documentation

See `apilist.md` for complete API specification.

## Development

The application follows clean architecture principles with:
- Repository pattern for data access
- Service layer for business logic
- Handler layer for HTTP concerns
- Dependency injection for testability
