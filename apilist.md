End-to-End API Specification
The following RESTful API provides the complete contract between the frontend client and the Go backend. All endpoints are versioned under the /api/v1/ prefix to ensure future compatibility. The design adheres to REST principles, using nouns for resources and HTTP verbs for actions.   

API Endpoint Summary
HTTP Method

Endpoint Path

Description

Auth Required

POST

/auth/register

Register a new user and create their profile.

No

POST

/auth/login

Log in a user, returning access and refresh tokens.

No

POST

/auth/refresh-token

Get a new access token using a valid refresh token.

No

POST

/auth/logout

Log out the user by revoking their refresh token.

Yes

GET

/users/me

Get the authenticated user's profile information.

Yes

PUT

/users/me

Update the authenticated user's profile.

Yes

GET

/content/subjects

Get a list of all available subjects.

No

GET

/content/exams

Get a list of all available competitive exams.

No

GET

/content/subjects/{subjectId}/topics

Get all topics for a specific subject.

No

POST

/tests

Create a new test and get the first question.

Yes

POST

/tests/regenerate

Create a new test based on weak areas from a prior test.

Yes

GET

/tests/{testId}/questions/{questionOrder}

Fetch a specific question by its order in a test.

Yes

POST

/tests/{testId}/answers

Submit an answer for a question in a test.

Yes

POST

/tests/{testId}/complete

Mark a test as complete and trigger analysis.

Yes

GET

/analysis/tests/{testId}

Get the detailed analysis and scorecard for a test.

Yes

GET

/analysis/dashboard

Get summary data for the main user dashboard.

Yes

GET

/analysis/topics/{topicId}

Get historical performance data for a specific topic.

Yes


Export to Sheets
Detailed Endpoint Documentation
Group 1: Authentication (/api/v1/auth)
POST /auth/register

Description: Creates a new user account and associated profile.

Request Body: { "email": "<EMAIL>", "password": "strong_password", "fullName": "John Doe", "age": 17,... }

Success Response (201 Created): { "userId": "...", "email": "...", "fullName": "..." }

POST /auth/login

Description: Authenticates a user and returns JWTs.

Request Body: { "email": "<EMAIL>", "password": "strong_password" }

Success Response (200 OK): { "accessToken": "...", "refreshToken": "..." }

POST /auth/refresh-token

Description: Issues a new access token.

Request Body: { "refreshToken": "..." }

Success Response (200 OK): { "accessToken": "..." }

POST /auth/logout

Description: Invalidates the user's current refresh token.

Request Body: { "refreshToken": "..." }

Success Response (204 No Content):

Group 2: User & Profile (/api/v1/users)
GET /users/me

Description: Retrieves the profile of the currently logged-in user.

Success Response (200 OK): { "userId": "...", "email": "...", "fullName": "...", "age": 17,... }

PUT /users/me

Description: Updates the profile of the currently logged-in user.

Request Body: { "fullName": "Johnathan Doe", "age": 18,... }

Success Response (200 OK): { "userId": "...", "email": "...", "fullName": "Johnathan Doe",... }

Group 3: Tests (/api/v1/tests)
POST /tests

Description: Generates a new test based on user criteria.

Request Body: { "subjectId": 1, "numQuestions": 10, "difficultyLevels": ["easy", "medium"] }

Success Response (201 Created): Returns the new test metadata and the first question object.

POST /tests/regenerate

Description: Generates a new test focusing on weak areas from a previous test.

Request Body: { "sourceTestId": "uuid-of-previous-test", "numQuestions": 10 }

Success Response (201 Created): Returns the new test metadata and the first question object.

POST /tests/{testId}/answers

Description: Submits a user's answer to a question.

Request Body: { "questionId": "uuid-of-question", "selectedOptionIndex": 2 }

Success Response (200 OK): { "isCorrect": true, "correctOptionIndex": 2, "explanation": "..." }

POST /tests/{testId}/complete

Description: Finalizes the test and triggers the backend analysis process.

Success Response (200 OK): { "testId": "...", "message": "Test completed. Analysis is processing.", "analysisUrl": "/api/v1/analysis/tests/..." }

Group 4: Analysis (/api/v1/analysis)
GET /analysis/tests/{testId}

Description: Retrieves the full, detailed scorecard for a completed test.

Success Response (200 OK): A complex JSON object including the score, correct/incorrect counts, time taken, a list of strong topics, a list of weak topics, and a question-by-question breakdown with user answers and correct answers.

GET /analysis/dashboard

Description: Provides high-level summary data for the user's main dashboard.

Success Response (200 OK): { "overallProficiency": 78.5, "recentTests": [...], "strongestSubject": "...", "weakestSubject": "..." }

