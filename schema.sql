-- #############################################################################
-- # SECTION 1: CUSTOM TYPE DEFINITIONS
-- #############################################################################
-- Defines custom data types to enforce consistency for specific fields.

CREATE TYPE difficulty_level AS ENUM ('easy', 'medium', 'hard');
CREATE TYPE test_status AS ENUM ('pending', 'in_progress', 'completed', 'cancelled');

-- #############################################################################
-- # SECTION 2: TABLE DEFINITIONS (DDL)
-- #############################################################################
-- Creates all tables for the application's data model.

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- User Management & Authentication
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE user_profiles (
    user_id UUID PRIMARY KEY,
    full_name VARCHAR(255),
    age INT,
    board VARCHAR(100),
    class VARCHAR(100),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT fk_user FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE TABLE refresh_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    token_hash VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMPTZ NOT NULL,
    is_revoked BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT fk_user FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Content & Subject Matter
CREATE TABLE subjects (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT
);

CREATE TABLE exams (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT
);

CREATE TABLE topics (
    id SERIAL PRIMARY KEY,
    subject_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    CONSTRAINT fk_subject FOREIGN KEY(subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    UNIQUE(subject_id, name)
);

CREATE TABLE user_preferences (
    user_id UUID NOT NULL,
    subject_id INT,
    exam_id INT,
    PRIMARY KEY (user_id, subject_id, exam_id),
    CONSTRAINT fk_user FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_subject FOREIGN KEY(subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    CONSTRAINT fk_exam FOREIGN KEY(exam_id) REFERENCES exams(id) ON DELETE CASCADE
);

-- Question Bank
CREATE TABLE questions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    topic_id INT NOT NULL,
    content JSONB NOT NULL,
    difficulty difficulty_level,
    author_ai_model VARCHAR(100),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT fk_topic FOREIGN KEY(topic_id) REFERENCES topics(id) ON DELETE SET NULL
);

-- Test Lifecycle
CREATE TABLE tests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    test_context_subject_id INT,
    test_context_exam_id INT,
    status test_status NOT NULL DEFAULT 'pending',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    CONSTRAINT fk_user FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_subject FOREIGN KEY(test_context_subject_id) REFERENCES subjects(id) ON DELETE SET NULL,
    CONSTRAINT fk_exam FOREIGN KEY(test_context_exam_id) REFERENCES exams(id) ON DELETE SET NULL
);

CREATE TABLE test_questions (
    id SERIAL PRIMARY KEY,
    test_id UUID NOT NULL,
    question_id UUID NOT NULL,
    question_order INT NOT NULL,
    CONSTRAINT fk_test FOREIGN KEY(test_id) REFERENCES tests(id) ON DELETE CASCADE,
    CONSTRAINT fk_question FOREIGN KEY(question_id) REFERENCES questions(id) ON DELETE CASCADE,
    UNIQUE(test_id, question_id),
    UNIQUE(test_id, question_order)
);

CREATE TABLE user_answers (
    id SERIAL PRIMARY KEY,
    test_question_id INT UNIQUE NOT NULL,
    selected_option_index INT NOT NULL,
    is_correct BOOLEAN NOT NULL,
    answered_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT fk_test_question FOREIGN KEY(test_question_id) REFERENCES test_questions(id) ON DELETE CASCADE
);

-- Performance Analytics
CREATE TABLE test_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    test_id UUID UNIQUE NOT NULL,
    user_id UUID NOT NULL,
    score DECIMAL(5, 2) NOT NULL,
    total_questions INT NOT NULL,
    correct_answers INT NOT NULL,
    incorrect_answers INT NOT NULL,
    time_taken_seconds INT NOT NULL,
    CONSTRAINT fk_test FOREIGN KEY(test_id) REFERENCES tests(id) ON DELETE CASCADE,
    CONSTRAINT fk_user FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE TABLE topic_performance_summary (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL,
    topic_id INT NOT NULL,
    total_attempted INT NOT NULL DEFAULT 0,
    total_correct INT NOT NULL DEFAULT 0,
    proficiency_score DECIMAL(5, 2) NOT NULL DEFAULT 0.00,
    last_tested_at TIMESTAMPTZ,
    CONSTRAINT fk_user FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_topic FOREIGN KEY(topic_id) REFERENCES topics(id) ON DELETE CASCADE,
    UNIQUE(user_id, topic_id)
);

-- #############################################################################
-- # SECTION 3: INDEX CREATION
-- #############################################################################
-- Creates indexes on foreign keys and frequently queried columns to optimize performance.

-- Indexes for User Management
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_refresh_tokens_user_id ON refresh_tokens(user_id);
CREATE INDEX idx_refresh_tokens_token_hash ON refresh_tokens(token_hash);

-- Indexes for Content
CREATE INDEX idx_topics_subject_id ON topics(subject_id);

-- Indexes for Question Bank
CREATE INDEX idx_questions_topic_id ON questions(topic_id);
CREATE INDEX idx_questions_difficulty ON questions(difficulty);

-- Indexes for Test Lifecycle
CREATE INDEX idx_tests_user_id ON tests(user_id);
CREATE INDEX idx_test_questions_test_id ON test_questions(test_id);
CREATE INDEX idx_test_questions_question_id ON test_questions(question_id);

-- Indexes for Analytics (Crucial for dashboard performance)
CREATE INDEX idx_test_results_user_id ON test_results(user_id);
CREATE INDEX idx_topic_performance_summary_user_id_topic_id ON topic_performance_summary(user_id, topic_id);

-- #############################################################################
-- # SECTION 4: AUTOMATION WITH TRIGGERS AND FUNCTIONS
-- #############################################################################
-- Creates a function and trigger to automatically update the topic performance summary
-- after a test is completed. This pre-aggregation is vital for a fast dashboard.

CREATE OR REPLACE FUNCTION update_topic_performance_summary()
RETURNS TRIGGER AS $$
DECLARE
    rec RECORD;
    v_user_id UUID;
BEGIN
    -- Get the user_id from the test that was just completed
    SELECT user_id INTO v_user_id FROM tests WHERE id = NEW.test_id;

    -- Loop through each answer in the completed test
    FOR rec IN
        SELECT
            q.topic_id,
            ua.is_correct
        FROM user_answers ua
        JOIN test_questions tq ON ua.test_question_id = tq.id
        JOIN questions q ON tq.question_id = q.id
        WHERE tq.test_id = NEW.test_id
    LOOP
        -- Insert or update the summary for the user and topic
        INSERT INTO topic_performance_summary (user_id, topic_id, total_attempted, total_correct, last_tested_at)
        VALUES (v_user_id, rec.topic_id, 1, CASE WHEN rec.is_correct THEN 1 ELSE 0 END, NOW())
        ON CONFLICT (user_id, topic_id)
        DO UPDATE SET
            total_attempted = topic_performance_summary.total_attempted + 1,
            total_correct = topic_performance_summary.total_correct + (CASE WHEN rec.is_correct THEN 1 ELSE 0 END),
            last_tested_at = NOW();
    END LOOP;

    -- Recalculate proficiency score for all updated topics for that user
    -- A simple proficiency score is correct / attempted. More complex algorithms can be substituted here.
    UPDATE topic_performance_summary
    SET proficiency_score = (total_correct::DECIMAL / total_attempted) * 100
    WHERE user_id = v_user_id;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger that fires AFTER a new row is inserted into test_results
CREATE TRIGGER trg_update_topic_performance
AFTER INSERT ON test_results
FOR EACH ROW
EXECUTE FUNCTION update_topic_performance_summary();

-- #############################################################################
-- # SECTION 5: REPORTING VIEWS
-- #############################################################################
-- Creates a view to simplify fetching a user's test history with scores.

CREATE OR REPLACE VIEW v_user_test_history AS
SELECT
    t.id AS test_id,
    t.user_id,
    t.status,
    t.created_at,
    t.completed_at,
    s.name AS subject_name,
    e.name AS exam_name,
    tr.score,
    tr.total_questions,
    tr.correct_answers,
    tr.incorrect_answers,
    tr.time_taken_seconds
FROM tests t
LEFT JOIN subjects s ON t.test_context_subject_id = s.id
LEFT JOIN exams e ON t.test_context_exam_id = e.id
LEFT JOIN test_results tr ON t.id = tr.test_id
ORDER BY t.created_at DESC;