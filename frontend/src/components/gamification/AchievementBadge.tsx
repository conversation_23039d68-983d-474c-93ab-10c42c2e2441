import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Award, Trophy, Target, Zap, Medal, Star } from "lucide-react";

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: "award" | "trophy" | "target" | "zap" | "medal" | "star";
  progress?: number;
  maxProgress?: number;
  unlocked: boolean;
  rarity: "common" | "rare" | "epic" | "legendary";
}

interface AchievementBadgeProps {
  achievement: Achievement;
  size?: "sm" | "md" | "lg";
  showProgress?: boolean;
}

const iconMap = {
  award: Award,
  trophy: Trophy,
  target: Target,
  zap: Zap,
  medal: Medal,
  star: Star
};

const rarityColors = {
  common: "bg-gray-100 border-gray-300 text-gray-600",
  rare: "bg-blue-100 border-blue-300 text-blue-600", 
  epic: "bg-purple-100 border-purple-300 text-purple-600",
  legendary: "bg-yellow-100 border-yellow-300 text-yellow-600"
};

export function AchievementBadge({ 
  achievement, 
  size = "md", 
  showProgress = false 
}: AchievementBadgeProps) {
  const Icon = iconMap[achievement.icon];
  const progressPercentage = achievement.progress && achievement.maxProgress 
    ? (achievement.progress / achievement.maxProgress) * 100 
    : 0;

  const sizeClasses = {
    sm: "p-2",
    md: "p-3", 
    lg: "p-4"
  };

  const iconSizes = {
    sm: "h-4 w-4",
    md: "h-6 w-6",
    lg: "h-8 w-8"
  };

  return (
    <Card className={`${sizeClasses[size]} ${
      achievement.unlocked 
        ? rarityColors[achievement.rarity]
        : "bg-muted/50 border-border text-muted-foreground"
    } transition-all hover:scale-105`}>
      <CardContent className="p-0 text-center">
        <div className="flex flex-col items-center space-y-2">
          <div className={`${
            achievement.unlocked 
              ? "animate-pulse" 
              : "opacity-50"
          }`}>
            <Icon className={iconSizes[size]} />
          </div>
          
          <div>
            <h4 className={`font-semibold ${
              size === "sm" ? "text-xs" : size === "md" ? "text-sm" : "text-base"
            }`}>
              {achievement.title}
            </h4>
            
            {size !== "sm" && (
              <p className={`text-xs opacity-75 mt-1`}>
                {achievement.description}
              </p>
            )}
          </div>

          {showProgress && achievement.progress !== undefined && achievement.maxProgress && (
            <div className="w-full">
              <div className="w-full bg-background/20 rounded-full h-2">
                <div 
                  className="bg-current h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progressPercentage}%` }}
                />
              </div>
              <p className="text-xs mt-1">
                {achievement.progress}/{achievement.maxProgress}
              </p>
            </div>
          )}

          {achievement.unlocked && (
            <Badge variant="secondary" className="text-xs">
              Unlocked!
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );
}