import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Trophy, Medal, Award, Crown } from "lucide-react";

interface LeaderboardEntry {
  rank: number;
  name: string;
  score: number;
  avatar?: string;
  isCurrentUser?: boolean;
  streak: number;
  testsCompleted: number;
}

interface LeaderboardCardProps {
  entries: LeaderboardEntry[];
  title?: string;
  period?: "daily" | "weekly" | "monthly" | "all-time";
}

export function LeaderboardCard({ 
  entries, 
  title = "Leaderboard",
  period = "weekly"
}: LeaderboardCardProps) {
  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="h-5 w-5 text-yellow-500" />;
      case 2:
        return <Medal className="h-5 w-5 text-gray-400" />;
      case 3:
        return <Award className="h-5 w-5 text-amber-600" />;
      default:
        return null;
    }
  };

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1:
        return "text-yellow-600 bg-yellow-50 border-yellow-200";
      case 2:
        return "text-gray-600 bg-gray-50 border-gray-200";
      case 3:
        return "text-amber-600 bg-amber-50 border-amber-200";
      default:
        return "text-muted-foreground bg-background border-border";
    }
  };

  const periodLabels = {
    daily: "Today",
    weekly: "This Week", 
    monthly: "This Month",
    "all-time": "All Time"
  };

  return (
    <Card className="card-feature">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <Trophy className="h-5 w-5 mr-2 text-primary" />
            {title}
          </CardTitle>
          <Badge variant="outline">{periodLabels[period]}</Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {entries.map((entry) => (
            <div
              key={entry.rank}
              className={`flex items-center justify-between p-3 rounded-lg border transition-all ${
                entry.isCurrentUser 
                  ? "bg-primary/5 border-primary/20 ring-2 ring-primary/10" 
                  : "border-border hover:bg-muted/50"
              }`}
            >
              <div className="flex items-center space-x-3">
                <div className={`flex items-center justify-center h-8 w-8 rounded-full border-2 font-semibold text-sm ${
                  getRankColor(entry.rank)
                }`}>
                  {getRankIcon(entry.rank) || entry.rank}
                </div>
                
                <Avatar className="h-8 w-8">
                  <AvatarImage src={entry.avatar} />
                  <AvatarFallback>
                    {entry.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                  </AvatarFallback>
                </Avatar>
                
                <div>
                  <div className="font-medium text-sm">
                    {entry.name}
                    {entry.isCurrentUser && (
                      <Badge variant="secondary" className="ml-2 text-xs">You</Badge>
                    )}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {entry.testsCompleted} tests • {entry.streak} day streak
                  </div>
                </div>
              </div>
              
              <div className="text-right">
                <div className="font-bold text-gradient text-lg">
                  {entry.score}
                </div>
                <div className="text-xs text-muted-foreground">points</div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-4 pt-4 border-t border-border">
          <div className="text-center text-sm text-muted-foreground">
            🏆 Keep practicing to climb the leaderboard!
          </div>
        </div>
      </CardContent>
    </Card>
  );
}