import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Flame, Calendar, Target } from "lucide-react";

interface StreakCounterProps {
  currentStreak: number;
  longestStreak: number;
  dailyGoal: number;
  dailyProgress: number;
  streakStartDate?: string;
  nextMilestone?: number;
}

export function StreakCounter({
  currentStreak,
  longestStreak,
  dailyGoal,
  dailyProgress,
  streakStartDate,
  nextMilestone = 10
}: StreakCounterProps) {
  const progressPercentage = (dailyProgress / dailyGoal) * 100;
  const isGoalMet = dailyProgress >= dailyGoal;
  const daysUntilMilestone = nextMilestone - (currentStreak % nextMilestone);

  const getStreakIcon = () => {
    if (currentStreak >= 30) return "🔥";
    if (currentStreak >= 14) return "🌟";
    if (currentStreak >= 7) return "⚡";
    if (currentStreak >= 3) return "💪";
    return "🎯";
  };

  const getStreakColor = () => {
    if (currentStreak >= 30) return "from-red-500 to-orange-500";
    if (currentStreak >= 14) return "from-purple-500 to-pink-500";
    if (currentStreak >= 7) return "from-blue-500 to-cyan-500";
    if (currentStreak >= 3) return "from-green-500 to-emerald-500";
    return "from-primary to-secondary";
  };

  return (
    <Card className="card-feature overflow-hidden">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <Flame className="h-5 w-5 mr-2 text-orange-500" />
            Study Streak
          </div>
          {isGoalMet && (
            <Badge className="bg-success text-success-foreground">
              Goal Met! 🎉
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Current Streak Display */}
        <div className="text-center">
          <div className={`inline-flex items-center justify-center h-20 w-20 rounded-full bg-gradient-to-r ${getStreakColor()} text-white mb-2`}>
            <span className="text-2xl">{getStreakIcon()}</span>
          </div>
          <div className="text-3xl font-bold text-gradient mb-1">
            {currentStreak}
          </div>
          <div className="text-sm text-muted-foreground">
            {currentStreak === 1 ? "day" : "days"} in a row
          </div>
          {streakStartDate && (
            <div className="text-xs text-muted-foreground mt-1">
              Since {streakStartDate}
            </div>
          )}
        </div>

        {/* Daily Progress */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="flex items-center">
              <Target className="h-4 w-4 mr-1 text-primary" />
              Today's Goal
            </span>
            <span className="font-semibold">
              {dailyProgress}/{dailyGoal} tests
            </span>
          </div>
          <Progress value={progressPercentage} className="h-2" />
          <div className="text-xs text-muted-foreground text-center">
            {isGoalMet 
              ? "🎯 Daily goal completed!" 
              : `${dailyGoal - dailyProgress} more to go!`
            }
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 gap-4 pt-4 border-t border-border">
          <div className="text-center">
            <div className="text-lg font-bold text-gradient">
              {longestStreak}
            </div>
            <div className="text-xs text-muted-foreground">
              Longest Streak
            </div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-gradient">
              {daysUntilMilestone}
            </div>
            <div className="text-xs text-muted-foreground">
              Days to {nextMilestone}
            </div>
          </div>
        </div>

        {/* Encouragement Message */}
        <div className="bg-primary/5 p-3 rounded-lg border border-primary/20">
          <div className="text-center text-sm">
            {currentStreak === 0 && "🚀 Start your streak today!"}
            {currentStreak >= 1 && currentStreak < 3 && "💪 Great start! Keep it up!"}
            {currentStreak >= 3 && currentStreak < 7 && "⚡ You're on fire! Don't break the chain!"}
            {currentStreak >= 7 && currentStreak < 14 && "🌟 Amazing streak! You're building a great habit!"}
            {currentStreak >= 14 && currentStreak < 30 && "🔥 Incredible dedication! You're unstoppable!"}
            {currentStreak >= 30 && "🏆 Legendary streak! You're a study champion!"}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}