import { ResponsiveHeatMap } from '@nivo/heatmap';

export function WeaknessHeatmap() {
  const data = [
    {
      id: 'Mathematics',
      data: [
        { x: 'Algebra', y: 85 },
        { x: 'Geometry', y: 72 },
        { x: 'Trigonometry', y: 45 },
        { x: 'Calculus', y: 68 }
      ]
    },
    {
      id: 'Physics',
      data: [
        { x: 'Mechanics', y: 78 },
        { x: 'Thermodynamics', y: 52 },
        { x: 'Optics', y: 65 },
        { x: 'Electricity', y: 82 }
      ]
    },
    {
      id: 'Chemistry',
      data: [
        { x: 'Inorganic', y: 88 },
        { x: 'Organic', y: 38 },
        { x: 'Physical', y: 74 },
        { x: 'Analytical', y: 69 }
      ]
    }
  ];

  return (
    <div className="h-[300px] w-full">
      <ResponsiveHeatMap
        data={data}
        margin={{ top: 60, right: 90, bottom: 60, left: 90 }}
        valueFormat=">-.0f"
        axisTop={{
          tickSize: 5,
          tickPadding: 5,
          tickRotation: -90,
          legend: '',
          legendOffset: 46
        }}
        axisRight={{
          tickSize: 5,
          tickPadding: 5,
          tickRotation: 0,
          legend: 'Subjects',
          legendPosition: 'middle',
          legendOffset: 70
        }}
        axisLeft={{
          tickSize: 5,
          tickPadding: 5,
          tickRotation: 0,
          legend: 'Subjects',
          legendPosition: 'middle',
          legendOffset: -72
        }}
        colors={{
          type: 'diverging',
          scheme: 'red_yellow_blue',
          divergeAt: 0.5,
          minValue: 0,
          maxValue: 100
        }}
        emptyColor="#555555"
        legends={[
          {
            anchor: 'bottom',
            translateX: 0,
            translateY: 30,
            length: 400,
            thickness: 8,
            direction: 'row',
            tickPosition: 'after',
            tickSize: 3,
            tickSpacing: 4,
            tickOverlap: false,
            tickFormat: '>-.0s',
            title: 'Performance Score →',
            titleAlign: 'start',
            titleOffset: 4
          }
        ]}
        theme={{
          background: 'transparent',
          text: {
            fontSize: 11,
            fill: 'hsl(222, 47%, 11%)'
          },
          axis: {
            domain: {
              line: {
                stroke: 'hsl(214, 32%, 91%)',
                strokeWidth: 1
              }
            },
            legend: {
              text: {
                fontSize: 12,
                fill: 'hsl(215, 16%, 47%)'
              }
            },
            ticks: {
              line: {
                stroke: 'hsl(214, 32%, 91%)',
                strokeWidth: 1
              },
              text: {
                fontSize: 11,
                fill: 'hsl(215, 16%, 47%)'
              }
            }
          }
        }}
      />
    </div>
  );
}