import { ResponsiveLine } from '@nivo/line';

interface PerformanceData {
  date: string;
  Mathematics: number;
  Physics: number;
  Chemistry: number;
}

interface PerformanceChartProps {
  data: PerformanceData[];
}

export function PerformanceChart({ data }: PerformanceChartProps) {
  const chartData = [
    {
      id: 'Mathematics',
      color: 'hsl(217, 91%, 60%)',
      data: data.map(d => ({ x: d.date, y: d.Mathematics }))
    },
    {
      id: 'Physics',
      color: 'hsl(142, 71%, 45%)',
      data: data.map(d => ({ x: d.date, y: d.Physics }))
    },
    {
      id: 'Chemistry',
      color: 'hsl(267, 84%, 65%)',
      data: data.map(d => ({ x: d.date, y: d.Chemistry }))
    }
  ];

  return (
    <div className="h-[300px] w-full">
      <ResponsiveLine
        data={chartData}
        margin={{ top: 20, right: 110, bottom: 50, left: 60 }}
        xScale={{ type: 'point' }}
        yScale={{
          type: 'linear',
          min: 'auto',
          max: 'auto',
          stacked: false,
          reverse: false
        }}
        yFormat=" >-.2f"
        curve="cardinal"
        axisTop={null}
        axisRight={null}
        axisBottom={{
          tickSize: 5,
          tickPadding: 5,
          tickRotation: 0,
          legend: 'Time Period',
          legendOffset: 36,
          legendPosition: 'middle'
        }}
        axisLeft={{
          tickSize: 5,
          tickPadding: 5,
          tickRotation: 0,
          legend: 'Score (%)',
          legendOffset: -40,
          legendPosition: 'middle'
        }}
        pointSize={8}
        pointColor={{ theme: 'background' }}
        pointBorderWidth={3}
        pointBorderColor={{ from: 'serieColor' }}
        pointLabelYOffset={-12}
        useMesh={true}
        legends={[
          {
            anchor: 'bottom-right',
            direction: 'column',
            justify: false,
            translateX: 100,
            translateY: 0,
            itemsSpacing: 0,
            itemDirection: 'left-to-right',
            itemWidth: 80,
            itemHeight: 20,
            itemOpacity: 0.75,
            symbolSize: 12,
            symbolShape: 'circle',
            symbolBorderColor: 'rgba(0, 0, 0, .5)',
            effects: [
              {
                on: 'hover',
                style: {
                  itemBackground: 'rgba(0, 0, 0, .03)',
                  itemOpacity: 1
                }
              }
            ]
          }
        ]}
        theme={{
          background: 'transparent',
          text: {
            fontSize: 11,
            fill: 'hsl(222, 47%, 11%)'
          },
          axis: {
            domain: {
              line: {
                stroke: 'hsl(214, 32%, 91%)',
                strokeWidth: 1
              }
            },
            legend: {
              text: {
                fontSize: 12,
                fill: 'hsl(215, 16%, 47%)'
              }
            },
            ticks: {
              line: {
                stroke: 'hsl(214, 32%, 91%)',
                strokeWidth: 1
              },
              text: {
                fontSize: 11,
                fill: 'hsl(215, 16%, 47%)'
              }
            }
          },
          grid: {
            line: {
              stroke: 'hsl(214, 32%, 91%)',
              strokeWidth: 1
            }
          },
          crosshair: {
            line: {
              stroke: 'hsl(217, 91%, 60%)',
              strokeWidth: 1,
              strokeOpacity: 0.75,
              strokeDasharray: '6 6'
            }
          }
        }}
      />
    </div>
  );
}