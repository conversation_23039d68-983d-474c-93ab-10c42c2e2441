import { ResponsiveRadar } from '@nivo/radar';

export function SubjectRadarChart() {
  const data = [
    {
      subject: 'Mathematics',
      current: 75,
      target: 90
    },
    {
      subject: 'Physics',
      current: 82,
      target: 85
    },
    {
      subject: 'Chemistry',
      current: 78,
      target: 88
    },
    {
      subject: 'Biology',
      current: 65,
      target: 80
    },
    {
      subject: 'English',
      current: 88,
      target: 90
    }
  ];

  return (
    <div className="h-[300px] w-full">
      <ResponsiveRadar
        data={data}
        keys={['current', 'target']}
        indexBy="subject"
        valueFormat=">-.0f"
        margin={{ top: 40, right: 80, bottom: 40, left: 80 }}
        borderColor={{ from: 'color' }}
        gridLevels={5}
        gridShape="circular"
        gridLabelOffset={36}
        enableDots={true}
        dotSize={8}
        dotColor={{ theme: 'background' }}
        dotBorderWidth={2}
        colors={['hsl(217, 91%, 60%)', 'hsl(267, 84%, 65%)']}
        blendMode="multiply"
        fillOpacity={0.25}
        legends={[
          {
            anchor: 'top-left',
            direction: 'column',
            translateX: -50,
            translateY: -40,
            itemWidth: 80,
            itemHeight: 20,
            itemTextColor: 'hsl(222, 47%, 11%)',
            symbolSize: 12,
            symbolShape: 'circle',
            effects: [
              {
                on: 'hover',
                style: {
                  itemTextColor: 'hsl(217, 91%, 60%)'
                }
              }
            ]
          }
        ]}
        theme={{
          background: 'transparent',
          text: {
            fontSize: 11,
            fill: 'hsl(222, 47%, 11%)'
          },
          axis: {
            domain: {
              line: {
                stroke: 'hsl(214, 32%, 91%)',
                strokeWidth: 1
              }
            },
            legend: {
              text: {
                fontSize: 12,
                fill: 'hsl(215, 16%, 47%)'
              }
            },
            ticks: {
              line: {
                stroke: 'hsl(214, 32%, 91%)',
                strokeWidth: 1
              },
              text: {
                fontSize: 11,
                fill: 'hsl(215, 16%, 47%)'
              }
            }
          },
          grid: {
            line: {
              stroke: 'hsl(214, 32%, 91%)',
              strokeWidth: 1
            }
          }
        }}
      />
    </div>
  );
}