import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Keyboard, Mouse, Zap, BookOpen } from "lucide-react";

interface HelpDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function HelpDialog({ open, onOpenChange }: HelpDialogProps) {
  const keyboardShortcuts = [
    { key: "←/→", description: "Navigate between questions" },
    { key: "1-4", description: "Select answer options A-D" },
    { key: "F", description: "Flag/unflag current question" },
    { key: "B", description: "Bookmark current question" },
    { key: "H", description: "Show/hide hint" },
    { key: "Space", description: "Pause/resume test" },
    { key: "Enter", description: "Submit current answer" },
    { key: "Esc", description: "Close dialogs/modals" }
  ];

  const features = [
    {
      icon: Keyboard,
      title: "Keyboard Navigation",
      description: "Use keyboard shortcuts for faster navigation and interaction"
    },
    {
      icon: Mouse,
      title: "Click & Touch",
      description: "Full mouse and touch support for all interactions"
    },
    {
      icon: Zap,
      title: "Auto-Save",
      description: "Your progress is automatically saved every 10 seconds"
    },
    {
      icon: BookOpen,
      title: "Review Mode",
      description: "Review your answers before final submission"
    }
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Test Interface Help</DialogTitle>
          <DialogDescription>
            Learn how to navigate and use the test interface effectively
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <Keyboard className="h-5 w-5 mr-2" />
                Keyboard Shortcuts
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {keyboardShortcuts.map((shortcut, index) => (
                  <div key={index} className="flex items-center justify-between p-2 rounded-lg bg-muted/50">
                    <Badge variant="outline" className="font-mono">
                      {shortcut.key}
                    </Badge>
                    <span className="text-sm text-muted-foreground flex-1 ml-3">
                      {shortcut.description}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Key Features</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {features.map((feature, index) => (
                  <div key={index} className="flex items-start space-x-3 p-3 rounded-lg border border-border">
                    <div className="h-8 w-8 rounded-lg bg-primary/10 flex items-center justify-center flex-shrink-0">
                      <feature.icon className="h-4 w-4 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">{feature.title}</h4>
                      <p className="text-xs text-muted-foreground mt-1">{feature.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <div className="bg-primary/5 p-4 rounded-lg border border-primary/20">
            <h4 className="font-medium text-sm mb-2">💡 Pro Tips</h4>
            <ul className="text-xs text-muted-foreground space-y-1">
              <li>• Use flags to mark questions you want to review later</li>
              <li>• Bookmark important questions for future reference</li>
              <li>• Take advantage of hints when you're stuck</li>
              <li>• Monitor your time per question to stay on track</li>
              <li>• Use pause feature if you need a quick break</li>
            </ul>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}