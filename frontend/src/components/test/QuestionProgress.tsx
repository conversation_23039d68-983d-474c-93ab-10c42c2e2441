import { Progress } from "@/components/ui/progress";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Clock, CheckCircle2, AlertTriangle } from "lucide-react";

interface QuestionProgressProps {
  currentQuestion: number;
  totalQuestions: number;
  answeredCount: number;
  flaggedCount: number;
  bookmarkedCount: number;
  timePerQuestion?: number;
  estimatedTime?: number;
}

export function QuestionProgress({
  currentQuestion,
  totalQuestions,
  answeredCount,
  flaggedCount,
  bookmarkedCount,
  timePerQuestion,
  estimatedTime
}: QuestionProgressProps) {
  const progress = ((currentQuestion + 1) / totalQuestions) * 100;
  const completionRate = (answeredCount / totalQuestions) * 100;

  return (
    <Card className="border-primary/10 bg-gradient-to-r from-primary/5 to-secondary/5">
      <CardContent className="p-4 space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">
            Question {currentQuestion + 1} of {totalQuestions}
          </span>
          <div className="flex items-center space-x-2">
            {estimatedTime && (
              <Badge variant="outline" className="text-xs">
                <Clock className="h-3 w-3 mr-1" />
                ~{Math.floor(estimatedTime / 60)}:{String(estimatedTime % 60).padStart(2, '0')}
              </Badge>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Progress</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        <div className="grid grid-cols-3 gap-3 text-center">
          <div className="space-y-1">
            <div className="flex items-center justify-center text-success">
              <CheckCircle2 className="h-4 w-4 mr-1" />
              <span className="text-sm font-semibold">{answeredCount}</span>
            </div>
            <div className="text-xs text-muted-foreground">Answered</div>
          </div>

          <div className="space-y-1">
            <div className="flex items-center justify-center text-warning">
              <AlertTriangle className="h-4 w-4 mr-1" />
              <span className="text-sm font-semibold">{flaggedCount}</span>
            </div>
            <div className="text-xs text-muted-foreground">Flagged</div>
          </div>

          <div className="space-y-1">
            <div className="flex items-center justify-center text-primary">
              <span className="text-sm font-semibold">{bookmarkedCount}</span>
            </div>
            <div className="text-xs text-muted-foreground">Bookmarked</div>
          </div>
        </div>

        {timePerQuestion && (
          <div className="pt-2 border-t border-border/50">
            <div className="flex justify-between text-xs">
              <span className="text-muted-foreground">Avg. time/question</span>
              <span className="font-medium">{Math.round(timePerQuestion)}s</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}