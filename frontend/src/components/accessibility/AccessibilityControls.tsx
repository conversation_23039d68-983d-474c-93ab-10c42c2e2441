import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Accessibility, 
  Type, 
  Palette, 
  Volume2, 
  Eye,
  MousePointer,
  Keyboard,
  Sun,
  Moon,
  Monitor
} from "lucide-react";

interface AccessibilityControlsProps {
  fontSize: number;
  onFontSizeChange: (size: number) => void;
  highContrast: boolean;
  onHighContrastChange: (enabled: boolean) => void;
  reducedMotion: boolean;
  onReducedMotionChange: (enabled: boolean) => void;
  screenReader: boolean;
  onScreenReaderChange: (enabled: boolean) => void;
  keyboardNavigation: boolean;
  onKeyboardNavigationChange: (enabled: boolean) => void;
  theme: "light" | "dark" | "system";
  onThemeChange: (theme: "light" | "dark" | "system") => void;
  soundEnabled: boolean;
  onSoundEnabledChange: (enabled: boolean) => void;
}

export function AccessibilityControls({
  fontSize,
  onFontSizeChange,
  highContrast,
  onHighContrastChange,
  reducedMotion,
  onReducedMotionChange,
  screenReader,
  onScreenReaderChange,
  keyboardNavigation,
  onKeyboardNavigationChange,
  theme,
  onThemeChange,
  soundEnabled,
  onSoundEnabledChange
}: AccessibilityControlsProps) {
  const resetToDefaults = () => {
    onFontSizeChange(16);
    onHighContrastChange(false);
    onReducedMotionChange(false);
    onScreenReaderChange(false);
    onKeyboardNavigationChange(true);
    onThemeChange("system");
    onSoundEnabledChange(true);
  };

  return (
    <Card className="card-feature">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <Accessibility className="h-5 w-5 mr-2 text-primary" />
            Accessibility Settings
          </div>
          <Badge variant="outline" className="text-xs">
            WCAG Compliant
          </Badge>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Font Size */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Type className="h-4 w-4 mr-2 text-muted-foreground" />
              <label className="text-sm font-medium">Font Size</label>
            </div>
            <span className="text-sm text-muted-foreground">{fontSize}px</span>
          </div>
          <Slider
            value={[fontSize]}
            onValueChange={(value) => onFontSizeChange(value[0])}
            min={12}
            max={24}
            step={1}
            className="w-full"
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Small (12px)</span>
            <span>Large (24px)</span>
          </div>
        </div>

        {/* Theme Selection */}
        <div className="space-y-3">
          <div className="flex items-center">
            <Palette className="h-4 w-4 mr-2 text-muted-foreground" />
            <label className="text-sm font-medium">Theme</label>
          </div>
          <Select value={theme} onValueChange={onThemeChange}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="light">
                <div className="flex items-center">
                  <Sun className="h-4 w-4 mr-2" />
                  Light
                </div>
              </SelectItem>
              <SelectItem value="dark">
                <div className="flex items-center">
                  <Moon className="h-4 w-4 mr-2" />
                  Dark
                </div>
              </SelectItem>
              <SelectItem value="system">
                <div className="flex items-center">
                  <Monitor className="h-4 w-4 mr-2" />
                  System
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Toggle Controls */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Eye className="h-4 w-4 mr-2 text-muted-foreground" />
              <div>
                <label className="text-sm font-medium">High Contrast</label>
                <div className="text-xs text-muted-foreground">
                  Increase color contrast for better visibility
                </div>
              </div>
            </div>
            <Switch
              checked={highContrast}
              onCheckedChange={onHighContrastChange}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <MousePointer className="h-4 w-4 mr-2 text-muted-foreground" />
              <div>
                <label className="text-sm font-medium">Reduced Motion</label>
                <div className="text-xs text-muted-foreground">
                  Minimize animations and transitions
                </div>
              </div>
            </div>
            <Switch
              checked={reducedMotion}
              onCheckedChange={onReducedMotionChange}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Volume2 className="h-4 w-4 mr-2 text-muted-foreground" />
              <div>
                <label className="text-sm font-medium">Sound Effects</label>
                <div className="text-xs text-muted-foreground">
                  Audio feedback for interactions
                </div>
              </div>
            </div>
            <Switch
              checked={soundEnabled}
              onCheckedChange={onSoundEnabledChange}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Keyboard className="h-4 w-4 mr-2 text-muted-foreground" />
              <div>
                <label className="text-sm font-medium">Keyboard Navigation</label>
                <div className="text-xs text-muted-foreground">
                  Enhanced keyboard shortcuts and focus
                </div>
              </div>
            </div>
            <Switch
              checked={keyboardNavigation}
              onCheckedChange={onKeyboardNavigationChange}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Accessibility className="h-4 w-4 mr-2 text-muted-foreground" />
              <div>
                <label className="text-sm font-medium">Screen Reader Support</label>
                <div className="text-xs text-muted-foreground">
                  Enhanced ARIA labels and descriptions
                </div>
              </div>
            </div>
            <Switch
              checked={screenReader}
              onCheckedChange={onScreenReaderChange}
            />
          </div>
        </div>

        {/* Reset Button */}
        <div className="pt-4 border-t border-border">
          <Button 
            variant="outline" 
            onClick={resetToDefaults}
            className="w-full"
          >
            Reset to Defaults
          </Button>
        </div>

        {/* Accessibility Info */}
        <div className="bg-primary/5 p-3 rounded-lg border border-primary/20">
          <div className="text-sm">
            <div className="font-medium mb-1">♿ Accessibility Features</div>
            <ul className="text-xs text-muted-foreground space-y-1">
              <li>• Full keyboard navigation support</li>
              <li>• Screen reader compatible</li>
              <li>• WCAG 2.1 AA compliant</li>
              <li>• High contrast mode available</li>
              <li>• Customizable font sizes</li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}