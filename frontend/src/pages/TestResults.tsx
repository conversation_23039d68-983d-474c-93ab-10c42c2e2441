import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Navbar } from "@/components/layout/Navbar";
import { 
  Award, 
  Target, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  BookOpen,
  TrendingUp,
  RefreshCw,
  Home
} from "lucide-react";

interface Question {
  id: number;
  question: string;
  options: string[];
  correct: number;
  explanation: string;
  difficulty: "Easy" | "Medium" | "Hard";
  topic: string;
}

export default function TestResults() {
  const location = useLocation();
  const { answers, questions, timeSpent } = location.state || {};
  
  const [user] = useState({
    name: "<PERSON>",
    email: "<EMAIL>"
  });

  // Calculate results
  const calculateResults = () => {
    if (!answers || !questions) return { score: 0, correct: 0, total: 0, percentage: 0 };
    
    let correct = 0;
    questions.forEach((question: Question, index: number) => {
      if (answers[index] === question.correct) {
        correct++;
      }
    });
    
    const percentage = Math.round((correct / questions.length) * 100);
    return { score: correct, correct, total: questions.length, percentage };
  };

  const results = calculateResults();
  
  // Categorize performance by topic
  const topicPerformance = () => {
    if (!answers || !questions) return {};
    
    const topics: { [key: string]: { correct: number; total: number; questions: Question[] } } = {};
    
    questions.forEach((question: Question, index: number) => {
      if (!topics[question.topic]) {
        topics[question.topic] = { correct: 0, total: 0, questions: [] };
      }
      topics[question.topic].total++;
      topics[question.topic].questions.push(question);
      if (answers[index] === question.correct) {
        topics[question.topic].correct++;
      }
    });
    
    return topics;
  };

  const topicStats = topicPerformance();

  // Identify strengths and weaknesses
  const getStrengthsAndWeaknesses = () => {
    const strengths: string[] = [];
    const weaknesses: string[] = [];
    
    Object.entries(topicStats).forEach(([topic, stats]) => {
      const percentage = (stats.correct / stats.total) * 100;
      if (percentage >= 80) {
        strengths.push(topic);
      } else if (percentage < 60) {
        weaknesses.push(topic);
      }
    });
    
    return { strengths, weaknesses };
  };

  const { strengths, weaknesses } = getStrengthsAndWeaknesses();

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}m ${secs}s`;
  };

  return (
    <div className="min-h-screen bg-background">
      <Navbar isAuthenticated={true} user={user} />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="h-20 w-20 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center mx-auto mb-4">
            <Award className="h-10 w-10 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gradient mb-2">Test Completed!</h1>
          <p className="text-muted-foreground">Here's how you performed on your Mathematics practice test</p>
        </div>

        {/* Overall Results */}
        <Card className="card-analytics mb-8">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl">Your Score</CardTitle>
            <div className="text-6xl font-bold text-gradient my-4">
              {results.percentage}%
            </div>
            <CardDescription>
              You got {results.correct} out of {results.total} questions correct
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center p-4 rounded-lg border border-border">
                <CheckCircle className="h-8 w-8 text-success mx-auto mb-2" />
                <div className="text-2xl font-bold text-success">{results.correct}</div>
                <div className="text-sm text-muted-foreground">Correct</div>
              </div>
              <div className="text-center p-4 rounded-lg border border-border">
                <XCircle className="h-8 w-8 text-destructive mx-auto mb-2" />
                <div className="text-2xl font-bold text-destructive">{results.total - results.correct}</div>
                <div className="text-sm text-muted-foreground">Incorrect</div>
              </div>
              <div className="text-center p-4 rounded-lg border border-border">
                <Clock className="h-8 w-8 text-primary mx-auto mb-2" />
                <div className="text-2xl font-bold text-primary">{formatTime(timeSpent || 0)}</div>
                <div className="text-sm text-muted-foreground">Time Spent</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Topic Performance */}
            <Card className="card-feature">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Target className="h-5 w-5 mr-2 text-primary" />
                  Performance by Topic
                </CardTitle>
                <CardDescription>
                  See how you performed in different areas
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {Object.entries(topicStats).map(([topic, stats]) => {
                  const percentage = Math.round((stats.correct / stats.total) * 100);
                  return (
                    <div key={topic} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="font-medium">{topic}</span>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-muted-foreground">
                            {stats.correct}/{stats.total}
                          </span>
                          <Badge 
                            variant={percentage >= 80 ? "default" : percentage >= 60 ? "secondary" : "destructive"}
                          >
                            {percentage}%
                          </Badge>
                        </div>
                      </div>
                      <Progress value={percentage} className="h-2" />
                    </div>
                  );
                })}
              </CardContent>
            </Card>

            {/* Question Review */}
            <Card className="card-feature">
              <CardHeader>
                <CardTitle>Question Review</CardTitle>
                <CardDescription>
                  Review your answers and explanations
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {questions?.map((question: Question, index: number) => {
                  const userAnswer = answers?.[index];
                  const isCorrect = userAnswer === question.correct;
                  
                  return (
                    <div key={question.id} className="p-4 rounded-lg border border-border">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className={`h-8 w-8 rounded-full flex items-center justify-center ${
                            isCorrect ? 'bg-success text-success-foreground' : 'bg-destructive text-destructive-foreground'
                          }`}>
                            {isCorrect ? (
                              <CheckCircle className="h-5 w-5" />
                            ) : (
                              <XCircle className="h-5 w-5" />
                            )}
                          </div>
                          <div>
                            <span className="font-semibold">Question {index + 1}</span>
                            <div className="flex items-center space-x-2 mt-1">
                              <Badge variant="outline">{question.topic}</Badge>
                              <Badge variant={question.difficulty === "Hard" ? "destructive" : question.difficulty === "Medium" ? "default" : "secondary"}>
                                {question.difficulty}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <h4 className="font-medium mb-3">{question.question}</h4>
                      
                      <div className="space-y-2 mb-4">
                        {question.options.map((option, optIndex) => (
                          <div key={optIndex} className={`p-2 rounded border ${
                            optIndex === question.correct 
                              ? 'border-success bg-success/10' 
                              : optIndex === userAnswer && !isCorrect
                              ? 'border-destructive bg-destructive/10'
                              : 'border-border'
                          }`}>
                            <div className="flex items-center space-x-2">
                              <span className="font-medium">{String.fromCharCode(65 + optIndex)}.</span>
                              <span>{option}</span>
                              {optIndex === question.correct && (
                                <Badge variant="default" className="ml-auto">Correct</Badge>
                              )}
                              {optIndex === userAnswer && !isCorrect && (
                                <Badge variant="destructive" className="ml-auto">Your Answer</Badge>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                      
                      <div className="bg-muted/50 p-3 rounded-lg">
                        <h5 className="font-medium mb-1">Explanation:</h5>
                        <p className="text-sm text-muted-foreground">{question.explanation}</p>
                      </div>
                    </div>
                  );
                })}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Performance Summary */}
            <Card className="card-analytics">
              <CardHeader>
                <CardTitle>Performance Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>Overall Score</span>
                  <span className="font-bold text-gradient">{results.percentage}%</span>
                </div>
                <div className="flex justify-between">
                  <span>Grade</span>
                  <Badge variant={results.percentage >= 90 ? "default" : results.percentage >= 80 ? "secondary" : results.percentage >= 70 ? "outline" : "destructive"}>
                    {results.percentage >= 90 ? "A+" : results.percentage >= 80 ? "B+" : results.percentage >= 70 ? "C+" : "Needs Improvement"}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Time Efficiency</span>
                  <span className="text-sm text-muted-foreground">
                    {timeSpent ? Math.round(timeSpent / results.total) : 0}s per question
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* Strengths */}
            {strengths.length > 0 && (
              <Card className="card-analytics bg-gradient-to-br from-success/5 to-success/10 border-success/20">
                <CardHeader>
                  <CardTitle className="flex items-center text-success">
                    <CheckCircle className="h-5 w-5 mr-2" />
                    Strengths
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {strengths.map((topic) => (
                      <li key={topic} className="flex items-center space-x-2">
                        <div className="h-2 w-2 rounded-full bg-success"></div>
                        <span className="text-sm">{topic}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {/* Weaknesses */}
            {weaknesses.length > 0 && (
              <Card className="card-analytics bg-gradient-to-br from-warning/5 to-warning/10 border-warning/20">
                <CardHeader>
                  <CardTitle className="flex items-center text-warning">
                    <AlertTriangle className="h-5 w-5 mr-2" />
                    Areas to Improve
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ul className="space-y-2 mb-4">
                    {weaknesses.map((topic) => (
                      <li key={topic} className="flex items-center space-x-2">
                        <div className="h-2 w-2 rounded-full bg-warning"></div>
                        <span className="text-sm">{topic}</span>
                      </li>
                    ))}
                  </ul>
                  <Link to={`/tests/practice?topics=${weaknesses.join(',')}`}>
                    <Button size="sm" className="w-full btn-gradient">
                      <Target className="h-4 w-4 mr-2" />
                      Practice Weak Areas
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            )}

            {/* Actions */}
            <Card className="card-analytics">
              <CardContent className="p-4 space-y-3">
                <Link to="/tests/new">
                  <Button className="w-full btn-gradient">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Take Another Test
                  </Button>
                </Link>
                <Link to="/analytics">
                  <Button variant="outline" className="w-full">
                    <TrendingUp className="h-4 w-4 mr-2" />
                    View Analytics
                  </Button>
                </Link>
                <Link to="/dashboard">
                  <Button variant="outline" className="w-full">
                    <Home className="h-4 w-4 mr-2" />
                    Back to Dashboard
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}