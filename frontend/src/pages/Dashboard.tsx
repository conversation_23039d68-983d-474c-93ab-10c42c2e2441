import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Navbar } from "@/components/layout/Navbar";
import { 
  BookOpen, 
  Target, 
  TrendingUp, 
  Clock, 
  Award, 
  PlayCircle,
  BarChart3,
  Plus,
  Calendar,
  CheckCircle
} from "lucide-react";

export default function Dashboard() {
  const [user] = useState({
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "",
    grade: "Class 12",
    subject: "Mathematics"
  });

  const recentTests = [
    {
      id: 1,
      title: "Algebra Practice Test",
      subject: "Mathematics",
      score: 85,
      totalQuestions: 20,
      date: "2024-01-10",
      status: "completed",
      difficulty: "Medium"
    },
    {
      id: 2,
      title: "Organic Chemistry Quiz",
      subject: "Chemistry", 
      score: 72,
      totalQuestions: 15,
      date: "2024-01-08",
      status: "completed",
      difficulty: "Hard"
    },
    {
      id: 3,
      title: "Physics Mechanics",
      subject: "Physics",
      score: 0,
      totalQuestions: 25,
      date: "2024-01-12",
      status: "scheduled",
      difficulty: "Medium"
    }
  ];

  const weeklyProgress = [
    { day: "Mon", completed: 2 },
    { day: "Tue", completed: 1 },
    { day: "Wed", completed: 3 },
    { day: "Thu", completed: 2 },
    { day: "Fri", completed: 4 },
    { day: "Sat", completed: 1 },
    { day: "Sun", completed: 2 }
  ];

  const subjectProgress = [
    { subject: "Mathematics", progress: 78, weakAreas: ["Trigonometry", "Calculus"] },
    { subject: "Physics", progress: 65, weakAreas: ["Thermodynamics", "Optics"] },
    { subject: "Chemistry", progress: 82, weakAreas: ["Organic Chemistry"] }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Navbar 
        isAuthenticated={true} 
        user={user}
      />
      
      <div className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gradient mb-2">
            Welcome back, {user.name}! 👋
          </h1>
          <p className="text-muted-foreground">
            Ready to continue your learning journey? Here's your progress overview.
          </p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="card-analytics">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Tests Completed</p>
                  <p className="text-3xl font-bold text-gradient">24</p>
                </div>
                <div className="h-12 w-12 rounded-lg bg-gradient-to-r from-primary to-primary-light flex items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-white" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-sm">
                <TrendingUp className="h-4 w-4 text-success mr-1" />
                <span className="text-success">+12%</span>
                <span className="text-muted-foreground ml-1">from last week</span>
              </div>
            </CardContent>
          </Card>

          <Card className="card-analytics">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Average Score</p>
                  <p className="text-3xl font-bold text-gradient">82%</p>
                </div>
                <div className="h-12 w-12 rounded-lg bg-gradient-to-r from-success to-success flex items-center justify-center">
                  <Award className="h-6 w-6 text-white" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-sm">
                <TrendingUp className="h-4 w-4 text-success mr-1" />
                <span className="text-success">+5%</span>
                <span className="text-muted-foreground ml-1">improvement</span>
              </div>
            </CardContent>
          </Card>

          <Card className="card-analytics">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Time Studied</p>
                  <p className="text-3xl font-bold text-gradient">18h</p>
                </div>
                <div className="h-12 w-12 rounded-lg bg-gradient-to-r from-secondary to-secondary-dark flex items-center justify-center">
                  <Clock className="h-6 w-6 text-white" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-sm">
                <span className="text-muted-foreground">This week</span>
              </div>
            </CardContent>
          </Card>

          <Card className="card-analytics">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Streak</p>
                  <p className="text-3xl font-bold text-gradient">7</p>
                </div>
                <div className="h-12 w-12 rounded-lg bg-gradient-to-r from-warning to-warning flex items-center justify-center">
                  <Target className="h-6 w-6 text-white" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-sm">
                <span className="text-muted-foreground">Days in a row</span>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Quick Actions */}
            <Card className="card-feature">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <PlayCircle className="h-5 w-5 mr-2 text-primary" />
                  Quick Actions
                </CardTitle>
                <CardDescription>
                  Start a new test or continue your learning
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Link to="/tests/new">
                    <Button className="w-full btn-gradient h-16 text-left justify-start p-4">
                      <div>
                        <div className="font-semibold">Start New Test</div>
                        <div className="text-sm opacity-90">Create a diagnostic test</div>
                      </div>
                    </Button>
                  </Link>
                  
                  <Link to="/tests/practice">
                    <Button variant="outline" className="w-full h-16 text-left justify-start p-4 border-primary/20 hover:bg-primary/5">
                      <div>
                        <div className="font-semibold">Practice Weak Areas</div>
                        <div className="text-sm text-muted-foreground">Focus on improvement</div>
                      </div>
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>

            {/* Recent Tests */}
            <Card className="card-feature">
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Recent Tests</CardTitle>
                  <CardDescription>Your latest practice sessions</CardDescription>
                </div>
                <Link to="/tests">
                  <Button variant="outline" size="sm">View All</Button>
                </Link>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentTests.map((test) => (
                    <div key={test.id} className="flex items-center justify-between p-4 rounded-lg border border-border hover:bg-muted/50 transition-colors">
                      <div className="flex items-center space-x-4">
                        <div className="h-10 w-10 rounded-lg bg-gradient-to-r from-primary to-secondary flex items-center justify-center">
                          <BookOpen className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <h4 className="font-semibold">{test.title}</h4>
                          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                            <span>{test.subject}</span>
                            <span>•</span>
                            <span>{test.totalQuestions} questions</span>
                            <span>•</span>
                            <Badge variant={test.difficulty === "Hard" ? "destructive" : test.difficulty === "Medium" ? "default" : "secondary"}>
                              {test.difficulty}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        {test.status === "completed" ? (
                          <div className="text-2xl font-bold text-gradient">{test.score}%</div>
                        ) : (
                          <Badge variant="outline">Scheduled</Badge>
                        )}
                        <div className="text-sm text-muted-foreground">{test.date}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Subject Progress */}
            <Card className="card-analytics">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2 text-primary" />
                  Subject Progress
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {subjectProgress.map((subject, index) => (
                  <div key={index}>
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium">{subject.subject}</span>
                      <span className="text-sm text-gradient font-semibold">{subject.progress}%</span>
                    </div>
                    <Progress value={subject.progress} className="mb-2" />
                    <div className="text-xs text-muted-foreground">
                      Weak areas: {subject.weakAreas.join(", ")}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Weekly Activity */}
            <Card className="card-analytics">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2 text-primary" />
                  This Week
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-7 gap-2">
                  {weeklyProgress.map((day, index) => (
                    <div key={index} className="text-center">
                      <div className="text-xs text-muted-foreground mb-1">{day.day}</div>
                      <div className={`h-8 w-8 rounded-full flex items-center justify-center text-xs font-semibold mx-auto ${
                        day.completed > 2 
                          ? 'bg-gradient-to-r from-success to-success text-white' 
                          : day.completed > 0 
                          ? 'bg-primary/10 text-primary' 
                          : 'bg-muted text-muted-foreground'
                      }`}>
                        {day.completed}
                      </div>
                    </div>
                  ))}
                </div>
                <div className="mt-4 text-sm text-muted-foreground text-center">
                  Tests completed per day
                </div>
              </CardContent>
            </Card>

            {/* Upcoming Test Reminder */}
            <Card className="card-analytics bg-gradient-to-br from-primary/5 to-secondary/5 border-primary/20">
              <CardContent className="p-6">
                <div className="text-center">
                  <div className="h-12 w-12 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center mx-auto mb-4">
                    <Calendar className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="font-semibold mb-2">Upcoming Test</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Physics Mechanics scheduled for tomorrow
                  </p>
                  <Button size="sm" className="btn-gradient">
                    Start Early
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}