import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Navbar } from "@/components/layout/Navbar";
import { TestControls } from "@/components/test/TestControls";
import { QuestionProgress } from "@/components/test/QuestionProgress";
import { HelpDialog } from "@/components/test/HelpDialog";
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from "@/components/ui/tooltip";
import { 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  ArrowLeft, 
  ArrowRight,
  Flag,
  BookOpen,
  Lightbulb,
  Bookmark,
  Pause,
  Play,
  Volume2,
  VolumeX,
  Maximize,
  ChevronDown,
  ChevronUp,
  HelpCircle
} from "lucide-react";

interface Question {
  id: number;
  question: string;
  options: string[];
  correct: number;
  explanation: string;
  difficulty: "Easy" | "Medium" | "Hard";
  topic: string;
  hint?: string;
  estimatedTime?: number;
}

export default function TestInterface() {
  const navigate = useNavigate();
  const { testId } = useParams();
  const { toast } = useToast();
  
  const [user] = useState({
    name: "Alex Johnson",
    email: "<EMAIL>"
  });

  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<{ [key: number]: number }>({});
  const [timeRemaining, setTimeRemaining] = useState(1800); // 30 minutes
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [flaggedQuestions, setFlaggedQuestions] = useState<Set<number>>(new Set());
  const [bookmarkedQuestions, setBookmarkedQuestions] = useState<Set<number>>(new Set());
  const [hintsUsed, setHintsUsed] = useState<Set<number>>(new Set());
  const [showHint, setShowHint] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [questionStartTime, setQuestionStartTime] = useState(Date.now());
  const [questionTimes, setQuestionTimes] = useState<{ [key: number]: number }>({});
  const [reviewMode, setReviewMode] = useState(false);
  const [expandedSidebar, setExpandedSidebar] = useState(true);
  const [showHelp, setShowHelp] = useState(false);

  // Sample questions - in real app this would come from API
  const questions: Question[] = [
    {
      id: 1,
      question: "What is the derivative of f(x) = x³ + 2x² - 5x + 3?",
      options: [
        "3x² + 4x - 5",
        "3x² + 2x - 5",
        "x³ + 4x - 5",
        "3x² + 4x + 5"
      ],
      correct: 0,
      explanation: "Using the power rule: d/dx(x³) = 3x², d/dx(2x²) = 4x, d/dx(-5x) = -5, d/dx(3) = 0. Therefore, f'(x) = 3x² + 4x - 5.",
      difficulty: "Medium",
      topic: "Calculus",
      hint: "Remember to apply the power rule to each term separately: d/dx(x^n) = nx^(n-1)",
      estimatedTime: 120
    },
    {
      id: 2,
      question: "In triangle ABC, if angle A = 60°, side b = 8, and side c = 6, what is the length of side a?",
      options: ["2√7", "4√3", "2√13", "6√2"],
      correct: 2,
      explanation: "Using the law of cosines: a² = b² + c² - 2bc·cos(A). Substituting: a² = 64 + 36 - 96·cos(60°) = 100 - 48 = 52. Therefore, a = √52 = 2√13.",
      difficulty: "Hard", 
      topic: "Trigonometry",
      hint: "Use the law of cosines when you have two sides and the included angle. cos(60°) = 1/2",
      estimatedTime: 180
    },
    {
      id: 3,
      question: "What is the solution to the equation 2x + 5 = 13?",
      options: ["x = 4", "x = 3", "x = 5", "x = 6"],
      correct: 0,
      explanation: "Solving step by step: 2x + 5 = 13, subtract 5 from both sides: 2x = 8, divide by 2: x = 4.",
      difficulty: "Easy",
      topic: "Algebra",
      hint: "Isolate the variable by performing inverse operations on both sides of the equation.",
      estimatedTime: 60
    }
  ];

  // Timer effect
  useEffect(() => {
    if (timeRemaining > 0 && !isSubmitted && !isPaused) {
      const timer = setTimeout(() => setTimeRemaining(timeRemaining - 1), 1000);
      return () => clearTimeout(timer);
    } else if (timeRemaining === 0 && !isSubmitted) {
      handleSubmitTest();
    }
  }, [timeRemaining, isSubmitted, isPaused]);

  // Track question time
  useEffect(() => {
    setQuestionStartTime(Date.now());
  }, [currentQuestion]);

  // Auto-save progress
  useEffect(() => {
    const saveProgress = () => {
      localStorage.setItem('testProgress', JSON.stringify({
        currentQuestion,
        answers,
        timeRemaining,
        flaggedQuestions: Array.from(flaggedQuestions),
        bookmarkedQuestions: Array.from(bookmarkedQuestions),
        hintsUsed: Array.from(hintsUsed),
        questionTimes
      }));
    };
    
    const interval = setInterval(saveProgress, 10000); // Save every 10 seconds
    return () => clearInterval(interval);
  }, [currentQuestion, answers, timeRemaining, flaggedQuestions, bookmarkedQuestions, hintsUsed, questionTimes]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) return;
      
      const currentQ = questions[currentQuestion];
      
      switch (e.key) {
        case 'ArrowLeft':
          e.preventDefault();
          handlePreviousQuestion();
          break;
        case 'ArrowRight':
          e.preventDefault();
          handleNextQuestion();
          break;
        case '1':
        case '2':
        case '3':
        case '4':
          e.preventDefault();
          const optionIndex = parseInt(e.key) - 1;
          if (optionIndex < currentQ.options.length) {
            handleAnswerSelect(optionIndex.toString());
          }
          break;
        case 'f':
        case 'F':
          e.preventDefault();
          toggleFlag();
          break;
        case 'b':
        case 'B':
          e.preventDefault();
          toggleBookmark();
          break;
        case 'h':
        case 'H':
          e.preventDefault();
          toggleHint();
          break;
        case ' ':
          e.preventDefault();
          togglePause();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [currentQuestion]);

  // Sound notifications
  const playSound = (type: 'correct' | 'incorrect' | 'flag' | 'bookmark') => {
    if (!soundEnabled) return;
    // In a real app, you would play actual sound files here
    console.log(`Playing ${type} sound`);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleAnswerSelect = (value: string) => {
    setAnswers({ ...answers, [currentQuestion]: parseInt(value) });
  };

  const handleNextQuestion = () => {
    if (currentQuestion < questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1);
    }
  };

  const toggleFlag = () => {
    const newFlagged = new Set(flaggedQuestions);
    if (flaggedQuestions.has(currentQuestion)) {
      newFlagged.delete(currentQuestion);
    } else {
      newFlagged.add(currentQuestion);
    }
    setFlaggedQuestions(newFlagged);
    playSound('flag');
  };

  const toggleBookmark = () => {
    const newBookmarked = new Set(bookmarkedQuestions);
    if (bookmarkedQuestions.has(currentQuestion)) {
      newBookmarked.delete(currentQuestion);
    } else {
      newBookmarked.add(currentQuestion);
    }
    setBookmarkedQuestions(newBookmarked);
    playSound('bookmark');
  };

  const toggleHint = () => {
    if (!hintsUsed.has(currentQuestion)) {
      setHintsUsed(new Set([...hintsUsed, currentQuestion]));
    }
    setShowHint(!showHint);
  };

  const togglePause = () => {
    setIsPaused(!isPaused);
    toast({
      title: isPaused ? "Test Resumed" : "Test Paused",
      description: isPaused ? "Timer is now running" : "Timer is paused",
    });
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  const handleSubmitTest = () => {
    setIsSubmitted(true);
    toast({
      title: "Test Submitted!",
      description: "Your answers have been saved. Calculating results...",
    });
    
    // Navigate to results after a delay
    setTimeout(() => {
      navigate("/test-results", { 
        state: { 
          answers, 
          questions, 
          timeSpent: 1800 - timeRemaining 
        } 
      });
    }, 2000);
  };

  const progress = ((currentQuestion + 1) / questions.length) * 100;
  const answeredCount = Object.keys(answers).length;
  const currentQ = questions[currentQuestion];
  const avgTimePerQuestion = Object.values(questionTimes).length > 0 
    ? Object.values(questionTimes).reduce((a, b) => a + b, 0) / Object.values(questionTimes).length 
    : 0;

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-background">
        <Navbar isAuthenticated={true} user={user} />
        
        <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gradient mb-2">Mathematics Practice Test</h1>
            <p className="text-muted-foreground">Focus: Algebra, Calculus, and Trigonometry</p>
          </div>
          <div className="flex items-center space-x-4 mt-4 md:mt-0">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-primary" />
              <span className="font-mono text-lg font-semibold">
                {formatTime(timeRemaining)}
              </span>
            </div>
            <Badge variant={timeRemaining < 300 ? "destructive" : "default"}>
              {timeRemaining < 300 ? "Hurry up!" : "Time remaining"}
            </Badge>
          </div>
        </div>

        {/* Test Controls */}
        <div className="mb-6">
          <TestControls
            isPaused={isPaused}
            onPause={togglePause}
            soundEnabled={soundEnabled}
            onToggleSound={() => setSoundEnabled(!soundEnabled)}
            isFullscreen={isFullscreen}
            onToggleFullscreen={toggleFullscreen}
            timeRemaining={timeRemaining}
            onShowHelp={() => setShowHelp(true)}
          />
        </div>

        {/* Progress */}
        <div className="mb-6">
          <QuestionProgress
            currentQuestion={currentQuestion}
            totalQuestions={questions.length}
            answeredCount={answeredCount}
            flaggedCount={flaggedQuestions.size}
            bookmarkedCount={bookmarkedQuestions.size}
            timePerQuestion={avgTimePerQuestion}
            estimatedTime={currentQ.estimatedTime}
          />
        </div>

        <div className="grid lg:grid-cols-4 gap-6">
          {/* Question Panel */}
          <div className="lg:col-span-3">
            <Card className="card-feature">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="h-8 w-8 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center text-white font-semibold">
                      {currentQuestion + 1}
                    </div>
                    <div>
                      <Badge variant={currentQ.difficulty === "Hard" ? "destructive" : currentQ.difficulty === "Medium" ? "default" : "secondary"}>
                        {currentQ.difficulty}
                      </Badge>
                      <Badge variant="outline" className="ml-2">{currentQ.topic}</Badge>
                      {currentQ.estimatedTime && (
                        <Badge variant="secondary" className="ml-2">
                          <Clock className="h-3 w-3 mr-1" />
                          {Math.floor(currentQ.estimatedTime / 60)}:{String(currentQ.estimatedTime % 60).padStart(2, '0')}
                        </Badge>
                      )}
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={toggleBookmark}
                          className={bookmarkedQuestions.has(currentQuestion) ? "bg-primary/10 border-primary text-primary" : ""}
                        >
                          <Bookmark className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Bookmark question (B)</p>
                      </TooltipContent>
                    </Tooltip>
                    
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={toggleFlag}
                          className={flaggedQuestions.has(currentQuestion) ? "bg-warning/10 border-warning text-warning" : ""}
                        >
                          <Flag className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Flag for review (F)</p>
                      </TooltipContent>
                    </Tooltip>

                    {currentQ.hint && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={toggleHint}
                            className={showHint ? "bg-secondary/10 border-secondary text-secondary" : ""}
                          >
                            <Lightbulb className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Show hint (H)</p>
                        </TooltipContent>
                      </Tooltip>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h2 className="text-lg font-semibold mb-4">{currentQ.question}</h2>
                  
                  <RadioGroup
                    value={answers[currentQuestion]?.toString() || ""}
                    onValueChange={handleAnswerSelect}
                    className="space-y-3"
                  >
                    {currentQ.options.map((option, index) => (
                      <div key={index} className="flex items-center space-x-3 p-3 rounded-lg border border-border hover:bg-muted/50 transition-colors">
                        <RadioGroupItem value={index.toString()} id={`option-${index}`} />
                        <Label htmlFor={`option-${index}`} className="flex-1 cursor-pointer text-base">
                          <kbd className="mr-2 px-1 py-0.5 text-xs bg-muted rounded">{index + 1}</kbd>
                          {String.fromCharCode(65 + index)}. {option}
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>

                  {/* Hint Section */}
                  {showHint && currentQ.hint && (
                    <Card className="mt-6 bg-secondary/5 border-secondary/20">
                      <CardContent className="p-4">
                        <div className="flex items-start space-x-3">
                          <div className="h-8 w-8 rounded-full bg-secondary/10 flex items-center justify-center flex-shrink-0">
                            <Lightbulb className="h-4 w-4 text-secondary" />
                          </div>
                          <div>
                            <h4 className="font-medium text-sm mb-1">💡 Hint</h4>
                            <p className="text-sm text-muted-foreground">{currentQ.hint}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>

                {/* Navigation */}
                <div className="flex items-center justify-between pt-6 border-t border-border">
                  <Button
                    variant="outline"
                    onClick={handlePreviousQuestion}
                    disabled={currentQuestion === 0}
                  >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Previous
                  </Button>
                  
                  <div className="flex space-x-2">
                    {currentQuestion === questions.length - 1 ? (
                      <Button 
                        onClick={handleSubmitTest}
                        className="btn-gradient"
                        disabled={isSubmitted}
                      >
                        {isSubmitted ? "Submitting..." : "Submit Test"}
                      </Button>
                    ) : (
                      <Button
                        onClick={handleNextQuestion}
                        className="btn-gradient"
                      >
                        Next
                        <ArrowRight className="h-4 w-4 ml-2" />
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Question Navigator */}
            <Card className="card-analytics">
              <CardHeader>
                <CardTitle className="text-lg">Question Navigator</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-5 gap-2">
                  {questions.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentQuestion(index)}
                      className={`h-10 w-10 rounded-lg border-2 flex items-center justify-center text-sm font-semibold transition-all ${
                        index === currentQuestion
                          ? "border-primary bg-primary text-primary-foreground"
                          : answers[index] !== undefined
                          ? "border-success bg-success/10 text-success"
                          : "border-border text-muted-foreground hover:border-primary hover:text-primary"
                      }`}
                    >
                      {index + 1}
                      {flaggedQuestions.has(index) && (
                        <Flag className="h-2 w-2 absolute translate-x-2 -translate-y-2 text-warning" />
                      )}
                    </button>
                  ))}
                </div>
                <div className="mt-4 space-y-2 text-xs">
                  <div className="flex items-center space-x-2">
                    <div className="h-3 w-3 rounded border-2 border-success bg-success/10"></div>
                    <span>Answered</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="h-3 w-3 rounded border-2 border-primary bg-primary"></div>
                    <span>Current</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="h-3 w-3 rounded border-2 border-border"></div>
                    <span>Not answered</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Test Info */}
            <Card className="card-analytics">
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <BookOpen className="h-5 w-5 mr-2" />
                  Test Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="text-sm text-muted-foreground">Total Questions</div>
                  <div className="font-semibold">{questions.length}</div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">Time Limit</div>
                  <div className="font-semibold">30 minutes</div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">Difficulty</div>
                  <div className="font-semibold">Mixed</div>
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">Subjects</div>
                  <div className="font-semibold">Mathematics</div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card className="card-analytics bg-gradient-to-br from-primary/5 to-secondary/5 border-primary/20">
              <CardContent className="p-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-gradient mb-1">
                    {Math.round((answeredCount / questions.length) * 100)}%
                  </div>
                  <div className="text-sm text-muted-foreground">Completed</div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
        
        {/* Help Dialog */}
        <HelpDialog open={showHelp} onOpenChange={setShowHelp} />
      </div>
      
      {/* Pause Overlay */}
      {isPaused && (
        <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
          <Card className="p-8 text-center">
            <CardContent>
              <Pause className="h-16 w-16 mx-auto mb-4 text-primary" />
              <h2 className="text-2xl font-bold mb-2">Test Paused</h2>
              <p className="text-muted-foreground mb-6">Take your time. Click resume when ready.</p>
              <Button onClick={togglePause} className="btn-gradient">
                <Play className="h-4 w-4 mr-2" />
                Resume Test
              </Button>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
    </TooltipProvider>
  );
}