import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Navbar } from "@/components/layout/Navbar";
import { PerformanceChart } from "@/components/charts/PerformanceChart";
import { SubjectRadarChart } from "@/components/charts/SubjectRadarChart";
import { WeaknessHeatmap } from "@/components/charts/WeaknessHeatmap";
import { 
  TrendingUp, 
  TrendingDown,
  Target, 
  BookOpen, 
  Award, 
  AlertTriangle,
  CheckCircle,
  Plus,
  ArrowRight,
  Calendar,
  Filter
} from "lucide-react";

export default function Analytics() {
  const [user] = useState({
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: ""
  });

  const [timeFilter, setTimeFilter] = useState("month");

  const strengths = [
    { topic: "Linear Equations", score: 95, improvement: "+8%", subject: "Mathematics" },
    { topic: "Chemical Bonding", score: 92, improvement: "+12%", subject: "Chemistry" },
    { topic: "Waves & Oscillations", score: 88, improvement: "+5%", subject: "Physics" },
    { topic: "Coordinate Geometry", score: 87, improvement: "+15%", subject: "Mathematics" }
  ];

  const weaknesses = [
    { topic: "Trigonometry", score: 45, decline: "-3%", subject: "Mathematics", difficulty: "Hard" },
    { topic: "Thermodynamics", score: 52, decline: "-1%", subject: "Physics", difficulty: "Medium" },
    { topic: "Organic Chemistry", score: 38, decline: "-5%", subject: "Chemistry", difficulty: "Hard" },
    { topic: "Probability", score: 58, decline: "0%", subject: "Mathematics", difficulty: "Medium" }
  ];

  const recentInsights = [
    {
      type: "improvement",
      title: "Great progress in Chemistry!",
      description: "Your Chemical Bonding scores improved by 12% this week",
      action: "Keep practicing",
      icon: TrendingUp,
      color: "text-success"
    },
    {
      type: "concern",
      title: "Trigonometry needs attention",
      description: "Score dropped by 3% - consider focused practice",
      action: "Generate practice test",
      icon: TrendingDown,
      color: "text-destructive"
    },
    {
      type: "achievement",
      title: "7-day streak completed!",
      description: "You've been consistent with your practice",
      action: "View rewards",
      icon: Award,
      color: "text-warning"
    }
  ];

  const performanceData = [
    { date: "Week 1", Mathematics: 65, Physics: 72, Chemistry: 68 },
    { date: "Week 2", Mathematics: 68, Physics: 75, Chemistry: 71 },
    { date: "Week 3", Mathematics: 72, Physics: 78, Chemistry: 74 },
    { date: "Week 4", Mathematics: 75, Physics: 82, Chemistry: 78 }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Navbar isAuthenticated={true} user={user} />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gradient mb-2">Analytics Dashboard</h1>
            <p className="text-muted-foreground">
              Detailed insights into your learning progress and performance trends
            </p>
          </div>
          <div className="flex items-center space-x-4 mt-4 md:mt-0">
            <Select value={timeFilter} onValueChange={setTimeFilter}>
              <SelectTrigger className="w-[140px]">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
                <SelectItem value="quarter">This Quarter</SelectItem>
                <SelectItem value="year">This Year</SelectItem>
              </SelectContent>
            </Select>
            <Button className="btn-gradient">
              <Plus className="h-4 w-4 mr-2" />
              Generate Report
            </Button>
          </div>
        </div>

        {/* Key Insights */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          {recentInsights.map((insight, index) => (
            <Card key={index} className="card-analytics hover-lift">
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <div className={`h-10 w-10 rounded-lg bg-gradient-to-r from-primary to-secondary flex items-center justify-center`}>
                    <insight.icon className={`h-5 w-5 text-white`} />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold mb-1">{insight.title}</h3>
                    <p className="text-sm text-muted-foreground mb-3">{insight.description}</p>
                    <Button variant="outline" size="sm">
                      {insight.action}
                      <ArrowRight className="h-3 w-3 ml-1" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="strengths">Strengths</TabsTrigger>
            <TabsTrigger value="weaknesses">Weaknesses</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid lg:grid-cols-2 gap-6">
              {/* Performance Chart */}
              <Card className="chart-container">
                <CardHeader>
                  <CardTitle>Performance Trends</CardTitle>
                  <CardDescription>Your progress across subjects over time</CardDescription>
                </CardHeader>
                <CardContent>
                  <PerformanceChart data={performanceData} />
                </CardContent>
              </Card>

              {/* Subject Radar */}
              <Card className="chart-container">
                <CardHeader>
                  <CardTitle>Subject Mastery</CardTitle>
                  <CardDescription>Current proficiency levels by subject</CardDescription>
                </CardHeader>
                <CardContent>
                  <SubjectRadarChart />
                </CardContent>
              </Card>
            </div>

            {/* Weakness Heatmap */}
            <Card className="chart-container">
              <CardHeader>
                <CardTitle>Topic Performance Heatmap</CardTitle>
                <CardDescription>Visual representation of your performance across different topics</CardDescription>
              </CardHeader>
              <CardContent>
                <WeaknessHeatmap />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="strengths" className="space-y-6">
            <Card className="card-feature">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CheckCircle className="h-5 w-5 mr-2 text-success" />
                  Your Strong Areas
                </CardTitle>
                <CardDescription>
                  Topics where you excel - keep up the great work!
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-4">
                  {strengths.map((strength, index) => (
                    <div key={index} className="p-4 rounded-lg border border-success/20 bg-success/5">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h3 className="font-semibold">{strength.topic}</h3>
                          <Badge variant="secondary" className="text-xs mt-1">
                            {strength.subject}
                          </Badge>
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-success">{strength.score}%</div>
                          <div className="text-sm text-success flex items-center">
                            <TrendingUp className="h-3 w-3 mr-1" />
                            {strength.improvement}
                          </div>
                        </div>
                      </div>
                      <div className="flex justify-between items-center mt-4">
                        <span className="text-sm text-muted-foreground">Mastery Level: Excellent</span>
                        <Button variant="outline" size="sm">
                          Advanced Practice
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="weaknesses" className="space-y-6">
            <Card className="card-feature">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <AlertTriangle className="h-5 w-5 mr-2 text-warning" />
                  Areas for Improvement
                </CardTitle>
                <CardDescription>
                  Focus on these topics to boost your overall performance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-4">
                  {weaknesses.map((weakness, index) => (
                    <div key={index} className="p-4 rounded-lg border border-warning/20 bg-warning/5">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h3 className="font-semibold">{weakness.topic}</h3>
                          <div className="flex items-center space-x-2 mt-1">
                            <Badge variant="secondary" className="text-xs">
                              {weakness.subject}
                            </Badge>
                            <Badge 
                              variant={weakness.difficulty === "Hard" ? "destructive" : "default"}
                              className="text-xs"
                            >
                              {weakness.difficulty}
                            </Badge>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-warning">{weakness.score}%</div>
                          <div className="text-sm text-destructive flex items-center">
                            <TrendingDown className="h-3 w-3 mr-1" />
                            {weakness.decline}
                          </div>
                        </div>
                      </div>
                      <div className="flex justify-between items-center mt-4">
                        <span className="text-sm text-muted-foreground">Needs attention</span>
                        <Link to={`/tests/practice?topic=${weakness.topic}`}>
                          <Button size="sm" className="btn-gradient">
                            <Target className="h-3 w-3 mr-1" />
                            Practice Now
                          </Button>
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="trends" className="space-y-6">
            <div className="grid md:grid-cols-3 gap-6">
              <Card className="card-analytics">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Overall Improvement</p>
                      <p className="text-3xl font-bold text-gradient">+18%</p>
                    </div>
                    <TrendingUp className="h-8 w-8 text-success" />
                  </div>
                  <p className="text-sm text-muted-foreground mt-2">Since last month</p>
                </CardContent>
              </Card>

              <Card className="card-analytics">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Practice Frequency</p>
                      <p className="text-3xl font-bold text-gradient">5.2</p>
                    </div>
                    <Calendar className="h-8 w-8 text-primary" />
                  </div>
                  <p className="text-sm text-muted-foreground mt-2">Tests per week</p>
                </CardContent>
              </Card>

              <Card className="card-analytics">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Best Subject</p>
                      <p className="text-lg font-bold text-gradient">Chemistry</p>
                    </div>
                    <BookOpen className="h-8 w-8 text-secondary" />
                  </div>
                  <p className="text-sm text-muted-foreground mt-2">82% average score</p>
                </CardContent>
              </Card>
            </div>

            <Card className="chart-container">
              <CardHeader>
                <CardTitle>Detailed Performance Analysis</CardTitle>
                <CardDescription>
                  Deep dive into your learning patterns and trends
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold mb-2">Study Pattern Analysis</h4>
                      <ul className="space-y-2 text-sm text-muted-foreground">
                        <li>• Most productive time: 10 AM - 12 PM</li>
                        <li>• Preferred difficulty: Medium level questions</li>
                        <li>• Best performance day: Wednesdays</li>
                        <li>• Average session duration: 45 minutes</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">Improvement Recommendations</h4>
                      <ul className="space-y-2 text-sm text-muted-foreground">
                        <li>• Focus on Trigonometry concepts</li>
                        <li>• Practice more Thermodynamics problems</li>
                        <li>• Try advanced level Chemistry questions</li>
                        <li>• Maintain current Mathematics momentum</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}