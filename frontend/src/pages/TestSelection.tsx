import { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Navbar } from "@/components/layout/Navbar";
import { 
  BookOpen, 
  Clock, 
  Target, 
  PlayCircle,
  Settings,
  Calendar
} from "lucide-react";

export default function TestSelection() {
  const [user] = useState({
    name: "<PERSON>",
    email: "<EMAIL>"
  });

  const navigate = useNavigate();
  const [selectedSubject, setSelectedSubject] = useState("");
  const [selectedExam, setSelectedExam] = useState("");
  const [difficulty, setDifficulty] = useState("Medium");
  const [duration, setDuration] = useState("30");

  const subjects = [
    "Mathematics", "Physics", "Chemistry", "Biology", 
    "English", "History", "Geography", "Computer Science"
  ];

  const competitiveExams = [
    "JEE Main", "JEE Advanced", "NEET", "CAT", "GATE",
    "UPSC", "SSC", "Banking PO", "CLAT", "General Practice"
  ];

  const testTypes = [
    {
      id: "diagnostic",
      title: "Diagnostic Test",
      description: "Assess your current knowledge level",
      duration: "45 min",
      questions: "50 questions",
      difficulty: "Mixed"
    },
    {
      id: "practice", 
      title: "Practice Test",
      description: "Custom test based on your weak areas",
      duration: "30 min",
      questions: "25 questions", 
      difficulty: "Adaptive"
    },
    {
      id: "mock",
      title: "Mock Exam",
      description: "Full-length exam simulation",
      duration: "180 min",
      questions: "150 questions",
      difficulty: "Real Exam"
    }
  ];

  const handleStartTest = (testType: string) => {
    if (!selectedSubject || !selectedExam) {
      alert("Please select subject and exam type first!");
      return;
    }
    navigate(`/tests/demo-${testType}`);
  };

  return (
    <div className="min-h-screen bg-background">
      <Navbar isAuthenticated={true} user={user} />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gradient mb-2">Select Your Practice Test</h1>
            <p className="text-muted-foreground">
              Choose your subject and exam type to generate a personalized test
            </p>
          </div>

          {/* Test Configuration */}
          <Card className="card-feature mb-8">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="h-5 w-5 mr-2 text-primary" />
                Test Configuration
              </CardTitle>
              <CardDescription>
                Customize your practice test settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Subject</label>
                  <Select value={selectedSubject} onValueChange={setSelectedSubject}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select subject" />
                    </SelectTrigger>
                    <SelectContent>
                      {subjects.map((subject) => (
                        <SelectItem key={subject} value={subject}>{subject}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Competitive Exam</label>
                  <Select value={selectedExam} onValueChange={setSelectedExam}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select exam type" />
                    </SelectTrigger>
                    <SelectContent>
                      {competitiveExams.map((exam) => (
                        <SelectItem key={exam} value={exam}>{exam}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Difficulty Level</label>
                  <Select value={difficulty} onValueChange={setDifficulty}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Easy">Easy</SelectItem>
                      <SelectItem value="Medium">Medium</SelectItem>
                      <SelectItem value="Hard">Hard</SelectItem>
                      <SelectItem value="Mixed">Mixed (Adaptive)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Duration (minutes)</label>
                  <Select value={duration} onValueChange={setDuration}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="15">15 minutes</SelectItem>
                      <SelectItem value="30">30 minutes</SelectItem>
                      <SelectItem value="45">45 minutes</SelectItem>
                      <SelectItem value="60">60 minutes</SelectItem>
                      <SelectItem value="180">180 minutes</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Test Types */}
          <div className="grid md:grid-cols-3 gap-6">
            {testTypes.map((test) => (
              <Card key={test.id} className="card-feature hover-lift">
                <CardHeader>
                  <div className="h-12 w-12 rounded-lg bg-gradient-to-r from-primary to-secondary flex items-center justify-center mb-4">
                    {test.id === "diagnostic" && <Target className="h-6 w-6 text-white" />}
                    {test.id === "practice" && <BookOpen className="h-6 w-6 text-white" />}
                    {test.id === "mock" && <Calendar className="h-6 w-6 text-white" />}
                  </div>
                  <CardTitle>{test.title}</CardTitle>
                  <CardDescription>{test.description}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Duration:</span>
                    <Badge variant="outline">{test.duration}</Badge>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Questions:</span>
                    <Badge variant="outline">{test.questions}</Badge>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Difficulty:</span>
                    <Badge variant="secondary">{test.difficulty}</Badge>
                  </div>
                  
                  <Button 
                    className="w-full btn-gradient mt-6"
                    onClick={() => handleStartTest(test.id)}
                    disabled={!selectedSubject || !selectedExam}
                  >
                    <PlayCircle className="h-4 w-4 mr-2" />
                    Start Test
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Quick Start */}
          <Card className="card-analytics mt-8 bg-gradient-to-br from-primary/5 to-secondary/5 border-primary/20">
            <CardContent className="p-6 text-center">
              <h3 className="font-semibold mb-2">Quick Start with Mathematics & JEE Main</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Don't want to configure? Jump right into a sample test!
              </p>
              <Button 
                className="btn-gradient"
                onClick={() => navigate("/tests/demo-quick")}
              >
                <PlayCircle className="h-4 w-4 mr-2" />
                Start Quick Demo Test
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}