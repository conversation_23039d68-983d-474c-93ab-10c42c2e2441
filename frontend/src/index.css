@tailwind base;
@tailwind components;
@tailwind utilities;

/* Practice Test Generator Design System - Professional Educational Theme */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap');

@layer base {
  :root {
    /* Base Colors */
    --background: 250 100% 99%;
    --foreground: 222 47% 11%;

    /* Card System */
    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    /* Interactive Elements */
    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    /* Primary Brand Colors - Educational Blue */
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;
    --primary-light: 217 91% 70%;
    --primary-dark: 217 91% 50%;

    /* Secondary Colors - Academic Purple */
    --secondary: 267 84% 81%;
    --secondary-foreground: 222 47% 11%;
    --secondary-dark: 267 84% 65%;

    /* Success & Progress Colors */
    --success: 142 71% 45%;
    --success-foreground: 0 0% 100%;
    --success-light: 142 71% 90%;

    /* Warning Colors */
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;
    --warning-light: 38 92% 95%;

    /* Error Colors */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    --destructive-light: 0 84% 95%;

    /* Neutral System */
    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;
    --accent: 210 40% 96%;
    --accent-foreground: 222 47% 11%;

    /* Borders & Inputs */
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 217 91% 60%;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(217, 91%, 60%) 0%, hsl(267, 84%, 81%) 100%);
    --gradient-success: linear-gradient(135deg, hsl(142, 71%, 45%) 0%, hsl(142, 71%, 60%) 100%);
    --gradient-hero: linear-gradient(135deg, hsl(217, 91%, 60%) 0%, hsl(267, 84%, 65%) 50%, hsl(217, 91%, 70%) 100%);
    --gradient-card: linear-gradient(145deg, hsl(0, 0%, 100%) 0%, hsl(210, 40%, 98%) 100%);

    /* Shadows */
    --shadow-soft: 0 2px 10px hsl(217, 91%, 60%, 0.1);
    --shadow-medium: 0 4px 20px hsl(217, 91%, 60%, 0.15);
    --shadow-strong: 0 8px 30px hsl(217, 91%, 60%, 0.2);
    --shadow-glow: 0 0 20px hsl(217, 91%, 60%, 0.3);

    /* Layout */
    --radius: 0.75rem;
    --radius-sm: 0.5rem;
    --radius-lg: 1rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter antialiased;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-poppins;
  }
}

@layer components {
  /* Button Variants */
  .btn-gradient {
    @apply bg-gradient-to-r from-primary to-secondary text-primary-foreground shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105;
  }

  .btn-hero {
    @apply bg-gradient-to-r from-primary via-secondary to-primary-light text-primary-foreground px-8 py-4 rounded-lg font-semibold shadow-strong hover:shadow-glow transition-all duration-300 transform hover:scale-105;
  }

  .btn-success {
    @apply bg-gradient-to-r from-success to-success text-success-foreground shadow-md hover:shadow-lg;
  }

  /* Card Variants */
  .card-gradient {
    background: var(--gradient-card);
    @apply shadow-soft border border-border/50;
  }

  .card-feature {
    @apply bg-card border border-border rounded-lg shadow-medium hover:shadow-strong transition-all duration-300 transform hover:scale-[1.02];
  }

  .card-analytics {
    @apply bg-gradient-to-br from-card to-muted/30 border border-border rounded-xl shadow-medium backdrop-blur-sm;
  }

  /* Text Variants */
  .text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-hero {
    @apply text-5xl md:text-7xl font-bold text-gradient leading-tight;
  }

  /* Animation Classes */
  .animate-fade-in {
    @apply animate-in fade-in duration-700;
  }

  .animate-slide-up {
    @apply animate-in slide-in-from-bottom-4 duration-700;
  }

  .animate-scale-in {
    @apply animate-in zoom-in-95 duration-500;
  }

  /* Glass Effect */
  .glass {
    @apply bg-card/80 backdrop-blur-md border border-border/20 shadow-medium;
  }

  /* Interactive Elements */
  .hover-lift {
    @apply transition-all duration-300 hover:transform hover:scale-[1.02] hover:shadow-medium;
  }

  /* Chart Container */
  .chart-container {
    @apply bg-gradient-to-br from-card to-primary/5 rounded-xl p-6 shadow-soft border border-border/50;
  }
}